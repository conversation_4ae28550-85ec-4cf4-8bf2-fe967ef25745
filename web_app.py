#!/usr/bin/env python3
"""
Time Series Forecasting Web Application
Flask-based web interface for ARIMA forecasting system
"""

from flask import Flask, render_template, request, jsonify, send_file
import pandas as pd
import numpy as np
import json
import io
import base64
from datetime import datetime, timedelta
from statsmodels.tsa.arima.model import ARIMA
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

app = Flask(__name__)
app.secret_key = 'forecasting_app_secret_key_2024'

# Global variables for model and data
model = None
preprocessing_params = None
model_info = None
model_diagnostics = None
historical_data = None

def load_model_and_data():
    """Load the trained model and all related data"""
    global model, preprocessing_params, model_info, model_diagnostics, historical_data
    
    try:
        print("Loading model and data for web application...")
        
        # Load preprocessing parameters
        with open('preprocessing_parameters.json', 'r') as f:
            preprocessing_params = json.load(f)
        
        # Load model evaluation summary
        with open('model_evaluation_summary.json', 'r') as f:
            evaluation = json.load(f)
        model_info = evaluation['best_model']
        
        # Load model diagnostics
        with open('model_diagnostics_results.json', 'r') as f:
            model_diagnostics = json.load(f)
        
        # Load grid search results
        with open('grid_search_results.json', 'r') as f:
            grid_results = json.load(f)
        
        # Load historical data
        data = pd.read_csv('preprocessed_timeseries.csv')
        data['Date'] = pd.to_datetime(data['Date'])
        data = data.sort_values('Date').reset_index(drop=True)
        historical_data = data
        
        # Load original cleaned data for visualization
        original_data = pd.read_csv('cleaned_transaction_data.csv')
        original_data['Date'] = pd.to_datetime(original_data['Date'])
        
        # Aggregate to daily revenue for visualization
        daily_revenue = original_data.groupby('Date')['TotalValue'].sum().reset_index()
        daily_revenue.columns = ['Date', 'Revenue']
        
        # Store for visualization
        historical_data['OriginalRevenue'] = daily_revenue['Revenue'].values[:len(historical_data)]
        
        # Fit the model
        n_test = int(len(data) * 0.2)
        train_data = data.iloc[:-n_test].copy()
        train_series = train_data['ProcessedRevenue']
        
        order = tuple(grid_results['best_model_order'])
        arima_model = ARIMA(train_series, order=order)
        model = arima_model.fit()
        
        print(f"✅ Model loaded: ARIMA{order}")
        print(f"✅ Historical data: {len(historical_data)} observations")
        print(f"✅ Model diagnostics loaded")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading model and data: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def inverse_transform(values):
    """Apply inverse transformation to get original scale"""
    if preprocessing_params.get('log_transformed', False):
        return np.exp(values)
    return values

def create_plot_base64(fig):
    """Convert matplotlib figure to base64 string for web display"""
    img_buffer = io.BytesIO()
    fig.savefig(img_buffer, format='png', dpi=150, bbox_inches='tight')
    img_buffer.seek(0)
    img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
    plt.close(fig)
    return img_base64

@app.route('/')
def home():
    """Main dashboard page"""
    if model is None:
        return render_template('error.html', 
                             error="Model not loaded. Please check server configuration.")
    
    # Create summary statistics
    summary_stats = {
        'model_name': model_info['Model'],
        'model_type': model_info['Type'],
        'mape': f"{model_info['MAPE']:.2f}%",
        'mae': f"{model_info['MAE']:.4f}",
        'rmse': f"{model_info['RMSE']:.4f}",
        'aic': f"{model.aic:.2f}",
        'diagnostic_score': f"{model_diagnostics['diagnostic_summary']['diagnostic_score']:.1f}%",
        'data_points': len(historical_data),
        'date_range': f"{historical_data['Date'].min().strftime('%Y-%m-%d')} to {historical_data['Date'].max().strftime('%Y-%m-%d')}"
    }
    
    return render_template('dashboard.html', stats=summary_stats)

@app.route('/forecast')
def forecast_page():
    """Forecasting interface page"""
    if model is None:
        return render_template('error.html', 
                             error="Model not loaded. Please check server configuration.")
    
    return render_template('forecast.html')

@app.route('/model-info')
def model_info_page():
    """Model information and diagnostics page"""
    if model is None:
        return render_template('error.html', 
                             error="Model not loaded. Please check server configuration.")
    
    # Prepare model information
    model_details = {
        'basic_info': {
            'Model': model_info['Model'],
            'Type': model_info['Type'],
            'Order': str(model.model.order),
            'AIC': f"{model.aic:.2f}",
            'BIC': f"{model.bic:.2f}",
            'Log Likelihood': f"{model.llf:.2f}"
        },
        'performance': {
            'MAPE': f"{model_info['MAPE']:.2f}%",
            'MAE': f"{model_info['MAE']:.4f}",
            'RMSE': f"{model_info['RMSE']:.4f}",
            'Target MAPE': "<5%",
            'Target RMSE': "<10%"
        },
        'diagnostics': model_diagnostics['diagnostic_summary']['diagnostic_tests'],
        'diagnostic_score': model_diagnostics['diagnostic_summary']['diagnostic_score'],
        'recommendation': model_diagnostics['diagnostic_summary']['recommendation']
    }
    
    return render_template('model_info.html', model=model_details)

@app.route('/api/forecast', methods=['POST'])
def api_forecast():
    """API endpoint for generating forecasts"""
    if model is None:
        return jsonify({'error': 'Model not loaded'}), 500
    
    try:
        data = request.get_json()
        steps = int(data.get('steps', 30))
        include_confidence = data.get('confidence_intervals', True)
        confidence_level = float(data.get('confidence_level', 0.95))
        
        # Validate parameters
        if steps <= 0 or steps > 365:
            return jsonify({'error': 'Steps must be between 1 and 365'}), 400
        
        if confidence_level <= 0 or confidence_level >= 1:
            return jsonify({'error': 'Confidence level must be between 0 and 1'}), 400
        
        # Generate forecast
        forecast_result = model.get_forecast(steps=steps)
        forecasts = forecast_result.predicted_mean.values
        forecasts_original = inverse_transform(forecasts)
        
        # Generate forecast dates
        last_date = historical_data['Date'].max()
        forecast_dates = [(last_date + timedelta(days=i+1)).strftime('%Y-%m-%d') 
                         for i in range(steps)]
        
        # Prepare response
        response = {
            'forecasts': [
                {'date': date, 'value': float(value)}
                for date, value in zip(forecast_dates, forecasts_original)
            ],
            'summary': {
                'steps': steps,
                'mean': float(np.mean(forecasts_original)),
                'min': float(np.min(forecasts_original)),
                'max': float(np.max(forecasts_original)),
                'total': float(np.sum(forecasts_original)),
                'trend': 'increasing' if forecasts_original[-1] > forecasts_original[0] else 'decreasing'
            }
        }
        
        # Add confidence intervals if requested
        if include_confidence:
            conf_int = forecast_result.conf_int(alpha=1-confidence_level)
            conf_int_original = inverse_transform(conf_int.values)
            
            for i, forecast_item in enumerate(response['forecasts']):
                forecast_item['confidence_interval'] = {
                    'lower': float(conf_int_original[i, 0]),
                    'upper': float(conf_int_original[i, 1]),
                    'level': confidence_level
                }
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({'error': f'Forecast generation failed: {str(e)}'}), 500

@app.route('/api/historical-data')
def api_historical_data():
    """API endpoint for historical data"""
    if historical_data is None:
        return jsonify({'error': 'Historical data not loaded'}), 500
    
    try:
        # Return last 100 data points for visualization
        recent_data = historical_data.tail(100).copy()
        
        response = {
            'data': [
                {
                    'date': row['Date'].strftime('%Y-%m-%d'),
                    'revenue': float(row['OriginalRevenue']) if 'OriginalRevenue' in row else 0
                }
                for _, row in recent_data.iterrows()
            ],
            'summary': {
                'total_points': len(historical_data),
                'date_range': {
                    'start': historical_data['Date'].min().strftime('%Y-%m-%d'),
                    'end': historical_data['Date'].max().strftime('%Y-%m-%d')
                }
            }
        }
        
        return jsonify(response)
        
    except Exception as e:
        return jsonify({'error': f'Failed to load historical data: {str(e)}'}), 500

@app.route('/api/model-summary')
def api_model_summary():
    """API endpoint for model summary"""
    if model is None:
        return jsonify({'error': 'Model not loaded'}), 500
    
    return jsonify({
        'model_info': model_info,
        'diagnostics': {
            'score': model_diagnostics['diagnostic_summary']['diagnostic_score'],
            'recommendation': model_diagnostics['diagnostic_summary']['recommendation'],
            'tests': model_diagnostics['diagnostic_summary']['diagnostic_tests']
        },
        'parameters': {
            'aic': float(model.aic),
            'bic': float(model.bic),
            'order': model.model.order
        }
    })

@app.errorhandler(404)
def not_found(error):
    return render_template('error.html', error="Page not found"), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('error.html', error="Internal server error"), 500

if __name__ == '__main__':
    print("Starting Time Series Forecasting Web Application...")
    print("="*60)
    
    if load_model_and_data():
        print("✅ All components loaded successfully")
        print("\nWeb Application Features:")
        print("- Dashboard with model overview")
        print("- Interactive forecasting interface")
        print("- Model information and diagnostics")
        print("- Data visualization")
        print("- REST API endpoints")
        print("="*60)
        
        app.run(host='0.0.0.0', port=5002, debug=False)
    else:
        print("❌ Failed to load required components")
        print("Cannot start web application")
