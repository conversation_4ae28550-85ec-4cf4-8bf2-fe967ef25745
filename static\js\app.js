// Time Series Forecasting Web Application JavaScript

// Global variables
let forecastChart = null;
let isLoading = false;

// Utility functions
function showLoading(element) {
    if (element) {
        element.classList.add('loading');
        isLoading = true;
    }
}

function hideLoading(element) {
    if (element) {
        element.classList.remove('loading');
        isLoading = false;
    }
}

function showAlert(message, type = 'info', containerId = 'alertContainer') {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="fas fa-${getAlertIcon(type)} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    container.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

function getAlertIcon(type) {
    const icons = {
        'success': 'check-circle',
        'danger': 'exclamation-triangle',
        'warning': 'exclamation-circle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

function formatNumber(num, decimals = 2) {
    return parseFloat(num).toFixed(decimals);
}

function formatCurrency(num) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(num);
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// API functions
async function apiRequest(url, options = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...options.headers
            },
            ...options
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.error || `HTTP error! status: ${response.status}`);
        }
        
        return data;
    } catch (error) {
        console.error('API request failed:', error);
        throw error;
    }
}

async function generateForecast(params) {
    return await apiRequest('/api/forecast', {
        method: 'POST',
        body: JSON.stringify(params)
    });
}

async function getHistoricalData() {
    return await apiRequest('/api/historical-data');
}

async function getModelSummary() {
    return await apiRequest('/api/model-summary');
}

// Chart functions
function createForecastChart(canvasId, historicalData, forecastData, options = {}) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return null;
    
    // Destroy existing chart
    if (forecastChart) {
        forecastChart.destroy();
    }
    
    const datasets = [];
    
    // Historical data
    if (historicalData && historicalData.length > 0) {
        datasets.push({
            label: 'Historical Data',
            data: historicalData.map(item => ({
                x: item.date,
                y: item.value || item.revenue
            })),
            borderColor: '#6c757d',
            backgroundColor: 'rgba(108, 117, 125, 0.1)',
            borderWidth: 2,
            fill: false,
            pointRadius: 1
        });
    }
    
    // Forecast data
    if (forecastData && forecastData.length > 0) {
        datasets.push({
            label: 'Forecast',
            data: forecastData.map(item => ({
                x: item.date,
                y: item.value
            })),
            borderColor: '#0d6efd',
            backgroundColor: 'rgba(13, 110, 253, 0.1)',
            borderWidth: 3,
            fill: false,
            pointRadius: 3,
            pointBackgroundColor: '#0d6efd'
        });
        
        // Confidence intervals if available
        const hasConfidenceIntervals = forecastData.some(item => item.confidence_interval);
        if (hasConfidenceIntervals) {
            const upperBound = forecastData.map(item => ({
                x: item.date,
                y: item.confidence_interval ? item.confidence_interval.upper : item.value
            }));
            
            const lowerBound = forecastData.map(item => ({
                x: item.date,
                y: item.confidence_interval ? item.confidence_interval.lower : item.value
            }));
            
            datasets.push({
                label: 'Confidence Interval',
                data: upperBound.concat(lowerBound.reverse()),
                borderColor: 'rgba(13, 110, 253, 0.3)',
                backgroundColor: 'rgba(13, 110, 253, 0.2)',
                borderWidth: 1,
                fill: true,
                pointRadius: 0
            });
        }
    }
    
    forecastChart = new Chart(ctx, {
        type: 'line',
        data: { datasets },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const value = formatCurrency(context.parsed.y);
                            return `${context.dataset.label}: ${value}`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    type: 'time',
                    time: {
                        unit: 'day'
                    },
                    title: {
                        display: true,
                        text: 'Date'
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: 'Revenue ($)'
                    },
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value);
                        }
                    }
                }
            },
            ...options
        }
    });
    
    return forecastChart;
}

// Form validation
function validateForecastForm(formData) {
    const errors = [];
    
    const steps = parseInt(formData.get('steps'));
    if (isNaN(steps) || steps < 1 || steps > 365) {
        errors.push('Forecast steps must be between 1 and 365');
    }
    
    const confidenceLevel = parseFloat(formData.get('confidence_level'));
    if (isNaN(confidenceLevel) || confidenceLevel <= 0 || confidenceLevel >= 1) {
        errors.push('Confidence level must be between 0 and 1');
    }
    
    return errors;
}

// Export functions
function exportToCSV(data, filename) {
    const csvContent = convertToCSV(data);
    downloadFile(csvContent, filename, 'text/csv');
}

function exportToJSON(data, filename) {
    const jsonContent = JSON.stringify(data, null, 2);
    downloadFile(jsonContent, filename, 'application/json');
}

function convertToCSV(data) {
    if (!data || data.length === 0) return '';
    
    const headers = Object.keys(data[0]);
    const csvRows = [headers.join(',')];
    
    for (const row of data) {
        const values = headers.map(header => {
            const value = row[header];
            return typeof value === 'string' ? `"${value}"` : value;
        });
        csvRows.push(values.join(','));
    }
    
    return csvRows.join('\n');
}

function downloadFile(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
}

// Animation functions
function animateValue(element, start, end, duration = 1000) {
    const startTime = performance.now();
    
    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = start + (end - start) * easeOutQuart(progress);
        element.textContent = formatNumber(current);
        
        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }
    
    requestAnimationFrame(update);
}

function easeOutQuart(t) {
    return 1 - Math.pow(1 - t, 4);
}

// Initialize application
document.addEventListener('DOMContentLoaded', function() {
    // Add fade-in animation to main content
    const mainContent = document.querySelector('main');
    if (mainContent) {
        mainContent.classList.add('fade-in');
    }
    
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Add smooth scrolling
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    console.log('Time Series Forecasting Web Application initialized');
});

// Error handling
window.addEventListener('error', function(event) {
    console.error('JavaScript error:', event.error);
    showAlert('An unexpected error occurred. Please refresh the page and try again.', 'danger');
});

// Export global functions for use in templates
window.ForecastApp = {
    showLoading,
    hideLoading,
    showAlert,
    formatNumber,
    formatCurrency,
    formatDate,
    generateForecast,
    getHistoricalData,
    getModelSummary,
    createForecastChart,
    validateForecastForm,
    exportToCSV,
    exportToJSON,
    animateValue
};
