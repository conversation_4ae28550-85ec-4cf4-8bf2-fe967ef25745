#!/usr/bin/env python3
"""
Model Evaluation Script
This script evaluates and compares all ARIMA models built so far.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_all_model_results():
    """Load results from all model experiments"""
    print("Loading all model results...")
    
    results = {}
    
    # Load baseline ARIMA results
    try:
        with open('baseline_arima_results.json', 'r') as f:
            results['baseline_arima'] = json.load(f)
        print("✅ Loaded baseline ARIMA results")
    except FileNotFoundError:
        print("⚠️ Baseline ARIMA results not found")
        results['baseline_arima'] = None
    
    # Load grid search results
    try:
        with open('grid_search_results.json', 'r') as f:
            results['grid_search'] = json.load(f)
        print("✅ Loaded grid search results")
    except FileNotFoundError:
        print("⚠️ Grid search results not found")
        results['grid_search'] = None
    
    # Load SARIMA results (if available)
    try:
        with open('sarima_results.json', 'r') as f:
            results['sarima'] = json.load(f)
        print("✅ Loaded SARIMA results")
    except FileNotFoundError:
        print("⚠️ SARIMA results not found (may still be running)")
        results['sarima'] = None
    
    return results

def create_performance_comparison_table(results):
    """Create comprehensive performance comparison table"""
    print(f"\n{'='*80}")
    print("COMPREHENSIVE MODEL PERFORMANCE COMPARISON")
    print(f"{'='*80}")
    
    comparison_data = []
    
    # Process baseline ARIMA
    if results['baseline_arima']:
        baseline = results['baseline_arima']
        comparison_data.append({
            'Model': baseline['best_model'],
            'Type': 'Baseline ARIMA',
            'MAE': baseline['performance_metrics']['mae'],
            'RMSE': baseline['performance_metrics']['rmse'],
            'MAPE': baseline['performance_metrics']['mape'],
            'MAE_%': baseline['performance_metrics']['mae_percentage'],
            'RMSE_%': baseline['performance_metrics']['rmse_percentage'],
            'Meets_MAPE_Target': baseline['performance_metrics']['meets_targets'] == 'True' or baseline['performance_metrics']['meets_targets'] == True,
            'Meets_RMSE_Target': baseline['performance_metrics']['meets_targets'] == 'True' or baseline['performance_metrics']['meets_targets'] == True
        })
    
    # Process grid search
    if results['grid_search']:
        grid = results['grid_search']
        comparison_data.append({
            'Model': f"ARIMA{grid['best_model_order']}",
            'Type': 'Grid Search ARIMA',
            'MAE': grid['performance_metrics']['mae'],
            'RMSE': grid['performance_metrics']['rmse'],
            'MAPE': grid['performance_metrics']['mape'],
            'MAE_%': grid['performance_metrics']['mae_percentage'],
            'RMSE_%': grid['performance_metrics']['rmse_percentage'],
            'Meets_MAPE_Target': grid['performance_metrics']['meets_targets'] == 'True' or grid['performance_metrics']['meets_targets'] == True,
            'Meets_RMSE_Target': grid['performance_metrics']['meets_targets'] == 'True' or grid['performance_metrics']['meets_targets'] == True
        })
    
    # Process SARIMA
    if results['sarima']:
        sarima = results['sarima']
        comparison_data.append({
            'Model': f"SARIMA{sarima['best_model_order']}x{sarima['best_seasonal_order']}",
            'Type': 'SARIMA',
            'MAE': sarima['performance_metrics']['mae'],
            'RMSE': sarima['performance_metrics']['rmse'],
            'MAPE': sarima['performance_metrics']['mape'],
            'MAE_%': sarima['performance_metrics']['mae_percentage'],
            'RMSE_%': sarima['performance_metrics']['rmse_percentage'],
            'Meets_MAPE_Target': sarima['performance_metrics']['meets_targets'] == 'True' or sarima['performance_metrics']['meets_targets'] == True,
            'Meets_RMSE_Target': sarima['performance_metrics']['meets_targets'] == 'True' or sarima['performance_metrics']['meets_targets'] == True
        })
    
    if not comparison_data:
        print("❌ No model results available for comparison")
        return None
    
    # Create DataFrame for better formatting
    df = pd.DataFrame(comparison_data)
    
    # Display comparison table
    print("\nPerformance Metrics Comparison:")
    print("-" * 120)
    print(f"{'Model':<25} {'Type':<20} {'MAE':<10} {'RMSE':<10} {'MAPE':<10} {'MAE_%':<10} {'RMSE_%':<10} {'MAPE_OK':<8} {'RMSE_OK':<8}")
    print("-" * 120)
    
    for _, row in df.iterrows():
        mape_ok = "✅" if row['Meets_MAPE_Target'] else "❌"
        rmse_ok = "✅" if row['Meets_RMSE_Target'] else "❌"
        
        print(f"{row['Model']:<25} {row['Type']:<20} {row['MAE']:<10.4f} {row['RMSE']:<10.4f} "
              f"{row['MAPE']:<10.2f} {row['MAE_%']:<10.2f} {row['RMSE_%']:<10.2f} {mape_ok:<8} {rmse_ok:<8}")
    
    # Find best model
    best_model_idx = df['MAPE'].idxmin()
    best_model = df.iloc[best_model_idx]
    
    print(f"\n🏆 BEST MODEL BY MAPE: {best_model['Model']} ({best_model['Type']})")
    print(f"   MAPE: {best_model['MAPE']:.2f}%")
    print(f"   MAE: {best_model['MAE']:.4f}")
    print(f"   RMSE: {best_model['RMSE']:.4f}")
    
    # Check if any model meets targets
    models_meeting_targets = df[df['Meets_MAPE_Target'] & df['Meets_RMSE_Target']]
    
    if len(models_meeting_targets) > 0:
        print(f"\n✅ {len(models_meeting_targets)} model(s) meet accuracy targets:")
        for _, model in models_meeting_targets.iterrows():
            print(f"   - {model['Model']} ({model['Type']})")
    else:
        print(f"\n❌ NO MODELS MEET ACCURACY TARGETS")
        print("   Target: MAPE < 5% AND RMSE < 10% of mean")
        print("   Recommendation: Consider additional preprocessing or external variables")
    
    return df

def analyze_model_complexity_vs_performance(results):
    """Analyze the trade-off between model complexity and performance"""
    print(f"\n{'='*60}")
    print("MODEL COMPLEXITY vs PERFORMANCE ANALYSIS")
    print(f"{'='*60}")
    
    complexity_data = []
    
    # Calculate complexity scores
    if results['baseline_arima']:
        baseline = results['baseline_arima']
        # Extract order from model name (e.g., "ARIMA(5, 1, 5)")
        model_name = baseline['best_model']
        try:
            order_str = model_name.split('(')[1].split(')')[0]
            p, d, q = map(int, order_str.split(', '))
            complexity = p + q  # AR + MA parameters
        except:
            complexity = 5  # Default estimate
        
        complexity_data.append({
            'Model': model_name,
            'Type': 'Baseline',
            'Complexity': complexity,
            'MAPE': baseline['performance_metrics']['mape'],
            'AIC': 'N/A'
        })
    
    if results['grid_search']:
        grid = results['grid_search']
        order = grid['best_model_order']
        complexity = order[0] + order[2]  # p + q
        
        complexity_data.append({
            'Model': f"ARIMA{order}",
            'Type': 'Grid Search',
            'Complexity': complexity,
            'MAPE': grid['performance_metrics']['mape'],
            'AIC': grid['model_aic']
        })
    
    if results['sarima']:
        sarima = results['sarima']
        order = sarima['best_model_order']
        seasonal_order = sarima['best_seasonal_order']
        complexity = order[0] + order[2] + seasonal_order[0] + seasonal_order[2]  # p + q + P + Q
        
        complexity_data.append({
            'Model': f"SARIMA{order}x{seasonal_order}",
            'Type': 'SARIMA',
            'Complexity': complexity,
            'MAPE': sarima['performance_metrics']['mape'],
            'AIC': sarima['model_aic']
        })
    
    if complexity_data:
        print("\nComplexity vs Performance:")
        print("-" * 80)
        print(f"{'Model':<30} {'Type':<15} {'Complexity':<10} {'MAPE':<10} {'AIC':<10}")
        print("-" * 80)
        
        for data in complexity_data:
            aic_str = f"{data['AIC']:.2f}" if data['AIC'] != 'N/A' else 'N/A'
            print(f"{data['Model']:<30} {data['Type']:<15} {data['Complexity']:<10} "
                  f"{data['MAPE']:<10.2f} {aic_str:<10}")
        
        # Find best complexity-performance trade-off
        valid_data = [d for d in complexity_data if d['AIC'] != 'N/A']
        if valid_data:
            # Simple scoring: lower MAPE and AIC is better, penalize high complexity
            for data in valid_data:
                data['score'] = data['MAPE'] + (data['AIC'] / 100) + (data['Complexity'] * 2)
            
            best_tradeoff = min(valid_data, key=lambda x: x['score'])
            print(f"\n🎯 BEST COMPLEXITY-PERFORMANCE TRADE-OFF: {best_tradeoff['Model']}")
            print(f"   Complexity: {best_tradeoff['Complexity']} parameters")
            print(f"   MAPE: {best_tradeoff['MAPE']:.2f}%")
            print(f"   AIC: {best_tradeoff['AIC']:.2f}")

def create_performance_visualization(results):
    """Create visualization comparing model performance"""
    print(f"\nCreating performance visualization...")
    
    models = []
    mape_values = []
    mae_values = []
    rmse_values = []
    
    if results['baseline_arima']:
        models.append('Baseline\nARIMA')
        mape_values.append(results['baseline_arima']['performance_metrics']['mape'])
        mae_values.append(results['baseline_arima']['performance_metrics']['mae'])
        rmse_values.append(results['baseline_arima']['performance_metrics']['rmse'])
    
    if results['grid_search']:
        models.append('Grid Search\nARIMA')
        mape_values.append(results['grid_search']['performance_metrics']['mape'])
        mae_values.append(results['grid_search']['performance_metrics']['mae'])
        rmse_values.append(results['grid_search']['performance_metrics']['rmse'])
    
    if results['sarima']:
        models.append('SARIMA')
        mape_values.append(results['sarima']['performance_metrics']['mape'])
        mae_values.append(results['sarima']['performance_metrics']['mae'])
        rmse_values.append(results['sarima']['performance_metrics']['rmse'])
    
    if not models:
        print("⚠️ No models available for visualization")
        return
    
    # Create comparison plots
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    # MAPE comparison
    bars1 = axes[0].bar(models, mape_values, color=['blue', 'green', 'red'][:len(models)])
    axes[0].set_title('MAPE Comparison')
    axes[0].set_ylabel('MAPE (%)')
    axes[0].axhline(y=5, color='red', linestyle='--', alpha=0.7, label='Target (5%)')
    axes[0].legend()
    
    # Add value labels on bars
    for bar, value in zip(bars1, mape_values):
        axes[0].text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(mape_values)*0.01,
                    f'{value:.1f}%', ha='center', va='bottom')
    
    # MAE comparison
    bars2 = axes[1].bar(models, mae_values, color=['blue', 'green', 'red'][:len(models)])
    axes[1].set_title('MAE Comparison')
    axes[1].set_ylabel('MAE')
    
    for bar, value in zip(bars2, mae_values):
        axes[1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(mae_values)*0.01,
                    f'{value:.3f}', ha='center', va='bottom')
    
    # RMSE comparison
    bars3 = axes[2].bar(models, rmse_values, color=['blue', 'green', 'red'][:len(models)])
    axes[2].set_title('RMSE Comparison')
    axes[2].set_ylabel('RMSE')
    
    for bar, value in zip(bars3, rmse_values):
        axes[2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(rmse_values)*0.01,
                    f'{value:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('model_performance_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ Performance visualization saved to: model_performance_comparison.png")

def generate_final_recommendations(results, comparison_df):
    """Generate final recommendations based on model evaluation"""
    print(f"\n{'='*80}")
    print("FINAL RECOMMENDATIONS")
    print(f"{'='*80}")
    
    if comparison_df is None or len(comparison_df) == 0:
        print("❌ No models available for recommendations")
        return
    
    # Find best performing model
    best_model = comparison_df.loc[comparison_df['MAPE'].idxmin()]
    
    print(f"🏆 RECOMMENDED MODEL: {best_model['Model']} ({best_model['Type']})")
    print(f"   Performance: MAPE = {best_model['MAPE']:.2f}%, MAE = {best_model['MAE']:.4f}, RMSE = {best_model['RMSE']:.4f}")
    
    # Check if targets are met
    if best_model['Meets_MAPE_Target'] and best_model['Meets_RMSE_Target']:
        print(f"✅ This model MEETS the accuracy targets")
        print(f"   Ready for production deployment")
    else:
        print(f"❌ This model does NOT meet accuracy targets")
        print(f"   Recommendations for improvement:")
        
        if not best_model['Meets_MAPE_Target']:
            print(f"   - MAPE ({best_model['MAPE']:.2f}%) > 5%: Consider external variables or ensemble methods")
        
        if not best_model['Meets_RMSE_Target']:
            print(f"   - RMSE ({best_model['RMSE_%']:.2f}%) > 10%: Consider longer training period or feature engineering")
    
    # Additional recommendations
    print(f"\n📋 ADDITIONAL RECOMMENDATIONS:")
    
    if len(comparison_df) > 1:
        mape_range = comparison_df['MAPE'].max() - comparison_df['MAPE'].min()
        if mape_range < 5:
            print(f"   - Model performance is similar across approaches (MAPE range: {mape_range:.2f}%)")
            print(f"   - Consider simpler model for production (lower complexity)")
        else:
            print(f"   - Significant performance differences found (MAPE range: {mape_range:.2f}%)")
            print(f"   - Grid search optimization shows clear benefits")
    
    # Check for seasonality
    if results['sarima'] and results['sarima']['seasonal_analysis']['seasonal_strength'] > 0.3:
        print(f"   - Strong seasonality detected (strength: {results['sarima']['seasonal_analysis']['seasonal_strength']:.3f})")
        print(f"   - SARIMA models recommended for capturing seasonal patterns")
    else:
        print(f"   - Weak seasonality - simple ARIMA models are sufficient")
    
    print(f"\n🚀 NEXT STEPS:")
    print(f"   1. Implement the recommended model in production")
    print(f"   2. Set up monitoring for model performance drift")
    print(f"   3. Consider retraining schedule (monthly/quarterly)")
    print(f"   4. Explore ensemble methods if accuracy targets not met")

def main():
    """Main model evaluation pipeline"""
    print("Starting comprehensive model evaluation...")
    
    # Load all results
    results = load_all_model_results()
    
    # Create performance comparison
    comparison_df = create_performance_comparison_table(results)
    
    # Analyze complexity vs performance
    analyze_model_complexity_vs_performance(results)
    
    # Create visualizations
    create_performance_visualization(results)
    
    # Generate recommendations
    generate_final_recommendations(results, comparison_df)
    
    # Save evaluation summary
    evaluation_summary = {
        'evaluation_date': datetime.now().isoformat(),
        'models_evaluated': len([r for r in results.values() if r is not None]),
        'best_model': comparison_df.loc[comparison_df['MAPE'].idxmin()].to_dict() if comparison_df is not None else None,
        'targets_met': any(comparison_df['Meets_MAPE_Target'] & comparison_df['Meets_RMSE_Target']) if comparison_df is not None else False,
        'comparison_table': comparison_df.to_dict('records') if comparison_df is not None else [],
        'recommendations': {
            'ready_for_production': comparison_df is not None and any(comparison_df['Meets_MAPE_Target'] & comparison_df['Meets_RMSE_Target']),
            'needs_improvement': comparison_df is not None and not any(comparison_df['Meets_MAPE_Target'] & comparison_df['Meets_RMSE_Target'])
        }
    }
    
    with open('model_evaluation_summary.json', 'w') as f:
        json.dump(evaluation_summary, f, indent=2, default=str)
    
    print(f"\n✅ Model evaluation completed!")
    print(f"✅ Results saved to: model_evaluation_summary.json")
    
    return evaluation_summary

if __name__ == "__main__":
    summary = main()
