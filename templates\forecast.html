{% extends "base.html" %}

{% block title %}Forecast - Time Series Forecasting{% endblock %}

{% block content %}
<div class="container">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="display-6 mb-0">
                        <i class="fas fa-crystal-ball text-primary me-2"></i>
                        Generate Forecast
                    </h1>
                    <p class="text-muted">Create time series forecasts using your ARIMA model</p>
                </div>
                <div>
                    <a href="{{ url_for('home') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Alert Container -->
    <div id="alertContainer"></div>

    <div class="row">
        <!-- Forecast Parameters -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sliders-h me-2"></i>
                        Forecast Parameters
                    </h5>
                </div>
                <div class="card-body">
                    <form id="forecastForm">
                        <div class="mb-3">
                            <label for="steps" class="form-label">
                                <i class="fas fa-calendar-alt me-1"></i>
                                Forecast Period (Days)
                            </label>
                            <input type="number" class="form-control" id="steps" name="steps" 
                                   value="30" min="1" max="365" required>
                            <div class="form-text">Number of days to forecast (1-365)</div>
                        </div>

                        <div class="mb-3">
                            <label for="confidence_level" class="form-label">
                                <i class="fas fa-percentage me-1"></i>
                                Confidence Level
                            </label>
                            <select class="form-select" id="confidence_level" name="confidence_level">
                                <option value="0.90">90%</option>
                                <option value="0.95" selected>95%</option>
                                <option value="0.99">99%</option>
                            </select>
                            <div class="form-text">Confidence interval level</div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="include_confidence" 
                                       name="include_confidence" checked>
                                <label class="form-check-label" for="include_confidence">
                                    Include confidence intervals
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="show_historical" 
                                       name="show_historical" checked>
                                <label class="form-check-label" for="show_historical">
                                    Show historical data
                                </label>
                            </div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg" id="generateBtn">
                                <i class="fas fa-magic me-2"></i>
                                Generate Forecast
                            </button>
                        </div>
                    </form>

                    <!-- Quick Presets -->
                    <hr class="my-4">
                    <h6 class="text-muted mb-3">Quick Presets</h6>
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="setPreset(7)">
                            <i class="fas fa-calendar-week me-1"></i>1 Week
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="setPreset(30)">
                            <i class="fas fa-calendar-alt me-1"></i>1 Month
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="setPreset(90)">
                            <i class="fas fa-calendar me-1"></i>3 Months
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="setPreset(180)">
                            <i class="fas fa-calendar-plus me-1"></i>6 Months
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Forecast Results -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-line text-primary me-2"></i>
                            Forecast Results
                        </h5>
                        <div id="exportButtons" style="display: none;">
                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-secondary btn-sm" onclick="exportForecast('csv')">
                                    <i class="fas fa-file-csv me-1"></i>CSV
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="exportForecast('json')">
                                    <i class="fas fa-file-code me-1"></i>JSON
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Initial State -->
                    <div id="initialState" class="text-center py-5">
                        <div class="mb-4">
                            <i class="fas fa-chart-line text-muted" style="font-size: 4rem;"></i>
                        </div>
                        <h4 class="text-muted mb-3">Ready to Generate Forecast</h4>
                        <p class="text-muted">
                            Configure your forecast parameters and click "Generate Forecast" to see predictions.
                        </p>
                    </div>

                    <!-- Loading State -->
                    <div id="loadingState" class="text-center py-5" style="display: none;">
                        <div class="mb-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <h4 class="text-muted mb-3">Generating Forecast...</h4>
                        <p class="text-muted">Please wait while we process your request.</p>
                    </div>

                    <!-- Chart Container -->
                    <div id="chartContainer" style="display: none;">
                        <canvas id="forecastChart" height="400"></canvas>
                    </div>

                    <!-- Forecast Summary -->
                    <div id="forecastSummary" style="display: none;" class="mt-4">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">Mean Forecast</h6>
                                        <h4 id="meanValue" class="mb-0">-</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">Total Revenue</h6>
                                        <h4 id="totalValue" class="mb-0">-</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">Min Value</h6>
                                        <h4 id="minValue" class="mb-0">-</h4>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body text-center">
                                        <h6 class="card-title">Max Value</h6>
                                        <h4 id="maxValue" class="mb-0">-</h4>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="card border-0 bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-info-circle text-primary me-1"></i>
                                            Forecast Details
                                        </h6>
                                        <ul class="list-unstyled mb-0">
                                            <li><strong>Period:</strong> <span id="forecastPeriod">-</span></li>
                                            <li><strong>Trend:</strong> <span id="forecastTrend">-</span></li>
                                            <li><strong>Confidence Level:</strong> <span id="confidenceLevel">-</span></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card border-0 bg-light">
                                    <div class="card-body">
                                        <h6 class="card-title">
                                            <i class="fas fa-exclamation-triangle text-warning me-1"></i>
                                            Important Notes
                                        </h6>
                                        <ul class="list-unstyled mb-0 small">
                                            <li>• Forecasts are based on historical patterns</li>
                                            <li>• Accuracy may decrease for longer periods</li>
                                            <li>• Consider external factors not in the model</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
let currentForecastData = null;
let currentHistoricalData = null;

document.addEventListener('DOMContentLoaded', function() {
    // Load historical data for chart
    loadHistoricalData();
    
    // Setup form submission
    document.getElementById('forecastForm').addEventListener('submit', handleForecastSubmission);
});

async function loadHistoricalData() {
    try {
        const data = await ForecastApp.getHistoricalData();
        currentHistoricalData = data.data.slice(-50); // Last 50 points for chart
    } catch (error) {
        console.error('Error loading historical data:', error);
        ForecastApp.showAlert('Failed to load historical data', 'warning');
    }
}

async function handleForecastSubmission(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const errors = ForecastApp.validateForecastForm(formData);
    
    if (errors.length > 0) {
        ForecastApp.showAlert(errors.join('<br>'), 'danger');
        return;
    }
    
    // Show loading state
    showLoadingState();
    
    try {
        const params = {
            steps: parseInt(formData.get('steps')),
            confidence_level: parseFloat(formData.get('confidence_level')),
            confidence_intervals: formData.get('include_confidence') === 'on'
        };
        
        const result = await ForecastApp.generateForecast(params);
        currentForecastData = result;
        
        // Show results
        showForecastResults(result, params);
        
        ForecastApp.showAlert('Forecast generated successfully!', 'success');
        
    } catch (error) {
        console.error('Forecast generation failed:', error);
        ForecastApp.showAlert(`Forecast generation failed: ${error.message}`, 'danger');
        showInitialState();
    }
}

function showLoadingState() {
    document.getElementById('initialState').style.display = 'none';
    document.getElementById('chartContainer').style.display = 'none';
    document.getElementById('forecastSummary').style.display = 'none';
    document.getElementById('loadingState').style.display = 'block';
    document.getElementById('exportButtons').style.display = 'none';
}

function showInitialState() {
    document.getElementById('loadingState').style.display = 'none';
    document.getElementById('chartContainer').style.display = 'none';
    document.getElementById('forecastSummary').style.display = 'none';
    document.getElementById('initialState').style.display = 'block';
    document.getElementById('exportButtons').style.display = 'none';
}

function showForecastResults(result, params) {
    // Hide loading and initial states
    document.getElementById('loadingState').style.display = 'none';
    document.getElementById('initialState').style.display = 'none';
    
    // Show chart and summary
    document.getElementById('chartContainer').style.display = 'block';
    document.getElementById('forecastSummary').style.display = 'block';
    document.getElementById('exportButtons').style.display = 'block';
    
    // Create chart
    const showHistorical = document.getElementById('show_historical').checked;
    const historicalData = showHistorical ? currentHistoricalData : [];
    
    ForecastApp.createForecastChart('forecastChart', historicalData, result.forecasts);
    
    // Update summary
    updateForecastSummary(result, params);
}

function updateForecastSummary(result, params) {
    // Animate values
    ForecastApp.animateValue(document.getElementById('meanValue'), 0, result.summary.mean);
    ForecastApp.animateValue(document.getElementById('totalValue'), 0, result.summary.total);
    ForecastApp.animateValue(document.getElementById('minValue'), 0, result.summary.min);
    ForecastApp.animateValue(document.getElementById('maxValue'), 0, result.summary.max);
    
    // Update text values after animation
    setTimeout(() => {
        document.getElementById('meanValue').textContent = ForecastApp.formatCurrency(result.summary.mean);
        document.getElementById('totalValue').textContent = ForecastApp.formatCurrency(result.summary.total);
        document.getElementById('minValue').textContent = ForecastApp.formatCurrency(result.summary.min);
        document.getElementById('maxValue').textContent = ForecastApp.formatCurrency(result.summary.max);
    }, 1000);
    
    // Update details
    document.getElementById('forecastPeriod').textContent = `${params.steps} days`;
    document.getElementById('forecastTrend').textContent = result.summary.trend || 'stable';
    document.getElementById('confidenceLevel').textContent = `${(params.confidence_level * 100).toFixed(0)}%`;
}

function setPreset(days) {
    document.getElementById('steps').value = days;
}

function exportForecast(format) {
    if (!currentForecastData) {
        ForecastApp.showAlert('No forecast data to export', 'warning');
        return;
    }
    
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `forecast_${timestamp}`;
    
    if (format === 'csv') {
        ForecastApp.exportToCSV(currentForecastData.forecasts, `${filename}.csv`);
    } else if (format === 'json') {
        ForecastApp.exportToJSON(currentForecastData, `${filename}.json`);
    }
    
    ForecastApp.showAlert(`Forecast exported as ${format.toUpperCase()}`, 'success');
}
</script>
{% endblock %}
