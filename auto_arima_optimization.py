#!/usr/bin/env python3
"""
Automated ARIMA Parameter Selection Script
This script uses auto_arima for optimal P, D, Q parameter selection.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import json
from statsmodels.tsa.arima.model import ARIMA
from sklearn.metrics import mean_absolute_error, mean_squared_error
from itertools import product
import warnings
warnings.filterwarnings('ignore')

def load_preprocessed_data():
    """Load preprocessed time series data"""
    print("Loading preprocessed data...")
    
    data = pd.read_csv('preprocessed_timeseries.csv')
    data['Date'] = pd.to_datetime(data['Date'])
    
    with open('preprocessing_parameters.json', 'r') as f:
        params = json.load(f)
    
    print(f"Loaded {len(data)} preprocessed observations")
    return data, params

def split_data_for_optimization(data, train_ratio=0.8):
    """Split data for auto_arima optimization"""
    n_train = int(len(data) * train_ratio)
    
    train_data = data.iloc[:n_train].copy()
    test_data = data.iloc[n_train:].copy()
    
    print(f"\nData split for optimization:")
    print(f"Training: {len(train_data)} observations")
    print(f"Testing: {len(test_data)} observations")
    
    return train_data, test_data

def run_grid_search_optimization(train_series, max_p=5, max_q=5, max_d=2):
    """Run grid search to find optimal ARIMA parameters"""
    print(f"\n{'='*60}")
    print("GRID SEARCH PARAMETER OPTIMIZATION")
    print(f"{'='*60}")

    print("Starting grid search optimization...")
    print(f"Search space: p(0-{max_p}), d(0-{max_d}), q(0-{max_q})")

    # Generate parameter combinations
    p_values = range(0, max_p + 1)
    d_values = range(0, max_d + 1)
    q_values = range(0, max_q + 1)

    best_aic = float('inf')
    best_order = None
    best_model = None
    results = []

    total_combinations = len(p_values) * len(d_values) * len(q_values)
    print(f"Testing {total_combinations} parameter combinations...")

    for i, (p, d, q) in enumerate(product(p_values, d_values, q_values)):
        try:
            # Skip invalid combinations
            if p == 0 and q == 0:
                continue

            model = ARIMA(train_series, order=(p, d, q))
            fitted_model = model.fit()

            aic = fitted_model.aic
            bic = fitted_model.bic

            results.append({
                'order': (p, d, q),
                'aic': aic,
                'bic': bic,
                'converged': fitted_model.mle_retvals['converged'] if hasattr(fitted_model, 'mle_retvals') else True
            })

            if aic < best_aic:
                best_aic = aic
                best_order = (p, d, q)
                best_model = fitted_model

            if (i + 1) % 10 == 0:
                print(f"Progress: {i + 1}/{total_combinations} combinations tested")

        except Exception as e:
            # Skip problematic parameter combinations
            continue

    if best_model is not None:
        print(f"\n✅ Grid search optimization completed!")
        print(f"🏆 Optimal model: ARIMA{best_order}")
        print(f"📊 AIC: {best_aic:.2f}")
        print(f"📊 BIC: {best_model.bic:.2f}")

        return best_model, best_order, results
    else:
        print(f"❌ Grid search optimization failed - no valid models found")
        return None, None, []

def evaluate_optimized_model(fitted_model, test_series, model_order):
    """Evaluate the optimized model performance"""
    print(f"\n{'='*50}")
    print("OPTIMIZED MODEL EVALUATION")
    print(f"{'='*50}")

    try:
        # Generate forecasts
        n_forecast = len(test_series)

        # Use get_forecast for better confidence interval handling
        forecast_result = fitted_model.get_forecast(steps=n_forecast)
        forecasts = forecast_result.predicted_mean
        conf_int = forecast_result.conf_int()

        # Calculate metrics
        mae = mean_absolute_error(test_series, forecasts)
        rmse = np.sqrt(mean_squared_error(test_series, forecasts))
        mape = np.mean(np.abs((test_series - forecasts) / test_series)) * 100

        # Calculate percentage metrics
        mean_actual = np.mean(test_series)
        mae_percentage = (mae / abs(mean_actual)) * 100 if mean_actual != 0 else float('inf')
        rmse_percentage = (rmse / abs(mean_actual)) * 100 if mean_actual != 0 else float('inf')

        print(f"📊 Optimized ARIMA{model_order} Performance Metrics:")
        print(f"   MAE: {mae:.4f} ({mae_percentage:.2f}% of mean)")
        print(f"   RMSE: {rmse:.4f} ({rmse_percentage:.2f}% of mean)")
        print(f"   MAPE: {mape:.2f}%")

        # Check accuracy targets
        meets_mape_target = mape < 5.0
        meets_rmse_target = rmse_percentage < 10.0

        print(f"   MAPE Target (<5%): {'✅ PASS' if meets_mape_target else '❌ FAIL'}")
        print(f"   RMSE Target (<10%): {'✅ PASS' if meets_rmse_target else '❌ FAIL'}")

        return {
            'forecasts': forecasts,
            'confidence_intervals': conf_int,
            'mae': mae,
            'rmse': rmse,
            'mape': mape,
            'mae_percentage': mae_percentage,
            'rmse_percentage': rmse_percentage,
            'meets_mape_target': meets_mape_target,
            'meets_rmse_target': meets_rmse_target,
            'overall_pass': meets_mape_target and meets_rmse_target
        }

    except Exception as e:
        print(f"❌ Error evaluating optimized model: {str(e)}")
        return None

def compare_with_baseline(auto_performance, baseline_file='baseline_arima_results.json'):
    """Compare auto_arima performance with baseline models"""
    print(f"\n{'='*50}")
    print("PERFORMANCE COMPARISON")
    print(f"{'='*50}")
    
    try:
        with open(baseline_file, 'r') as f:
            baseline_results = json.load(f)
        
        baseline_metrics = baseline_results['performance_metrics']
        
        print("Performance Comparison:")
        print(f"{'Metric':<15} {'Baseline':<15} {'Auto_ARIMA':<15} {'Improvement':<15}")
        print("-" * 60)
        
        # MAE comparison
        mae_improvement = ((baseline_metrics['mae'] - auto_performance['mae']) / baseline_metrics['mae']) * 100
        print(f"{'MAE':<15} {baseline_metrics['mae']:<15.4f} {auto_performance['mae']:<15.4f} {mae_improvement:<15.2f}%")
        
        # RMSE comparison
        rmse_improvement = ((baseline_metrics['rmse'] - auto_performance['rmse']) / baseline_metrics['rmse']) * 100
        print(f"{'RMSE':<15} {baseline_metrics['rmse']:<15.4f} {auto_performance['rmse']:<15.4f} {rmse_improvement:<15.2f}%")
        
        # MAPE comparison
        mape_improvement = ((baseline_metrics['mape'] - auto_performance['mape']) / baseline_metrics['mape']) * 100
        print(f"{'MAPE':<15} {baseline_metrics['mape']:<15.2f}% {auto_performance['mape']:<15.2f}% {mape_improvement:<15.2f}%")
        
        # Overall assessment
        auto_better = (mae_improvement > 0 and rmse_improvement > 0 and mape_improvement > 0)
        print(f"\n🎯 Auto_ARIMA vs Baseline: {'✅ BETTER' if auto_better else '⚠️ MIXED RESULTS'}")
        
        return {
            'mae_improvement': mae_improvement,
            'rmse_improvement': rmse_improvement,
            'mape_improvement': mape_improvement,
            'auto_arima_better': auto_better
        }
        
    except FileNotFoundError:
        print("⚠️ Baseline results not found - skipping comparison")
        return None

def create_auto_arima_visualization(train_data, test_data, forecasts, conf_int, model_order):
    """Create visualization for auto_arima results"""
    print(f"\nCreating auto_arima visualization...")
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))
    
    # Full time series plot
    ax1.plot(train_data['Date'], train_data['ProcessedRevenue'], 
             label='Training Data', color='blue', alpha=0.7)
    ax1.plot(test_data['Date'], test_data['ProcessedRevenue'], 
             label='Actual (Test)', color='green', linewidth=2)
    ax1.plot(test_data['Date'], forecasts, 
             label=f'Auto_ARIMA{model_order} Forecast', color='red', linewidth=2, linestyle='--')
    
    # Confidence intervals
    if conf_int is not None:
        ax1.fill_between(test_data['Date'],
                         conf_int.iloc[:, 0], conf_int.iloc[:, 1],
                         color='red', alpha=0.2, label='95% Confidence Interval')
    
    ax1.set_title(f'Auto_ARIMA{model_order} - Full Time Series Forecast')
    ax1.set_ylabel('Processed Revenue (Log-Differenced)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Zoomed view of test period
    ax2.plot(test_data['Date'], test_data['ProcessedRevenue'], 
             label='Actual', color='green', linewidth=3, marker='o', markersize=4)
    ax2.plot(test_data['Date'], forecasts, 
             label=f'Auto_ARIMA{model_order} Forecast', color='red', linewidth=3, 
             marker='s', markersize=4, linestyle='--')
    
    if conf_int is not None:
        ax2.fill_between(test_data['Date'],
                         conf_int.iloc[:, 0], conf_int.iloc[:, 1],
                         color='red', alpha=0.2, label='95% Confidence Interval')
    
    ax2.set_title('Test Period - Detailed View')
    ax2.set_xlabel('Date')
    ax2.set_ylabel('Processed Revenue (Log-Differenced)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('auto_arima_forecast_visualization.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ Auto_ARIMA visualization saved to: auto_arima_forecast_visualization.png")

def run_multiple_grid_search_configurations(train_series):
    """Run grid search with different configurations to find the best"""
    print(f"\n{'='*60}")
    print("MULTIPLE GRID SEARCH CONFIGURATIONS")
    print(f"{'='*60}")

    configurations = [
        {'name': 'Conservative', 'max_p': 3, 'max_q': 3, 'max_d': 2},
        {'name': 'Moderate', 'max_p': 5, 'max_q': 5, 'max_d': 2},
        {'name': 'Aggressive', 'max_p': 7, 'max_q': 7, 'max_d': 2},
    ]

    best_model = None
    best_aic = float('inf')
    best_config = None
    best_order = None
    all_results = {}

    for config in configurations:
        print(f"\nTesting {config['name']} configuration...")

        try:
            model, order, results = run_grid_search_optimization(
                train_series,
                max_p=config['max_p'],
                max_q=config['max_q'],
                max_d=config['max_d']
            )

            if model is not None:
                aic = model.aic
                all_results[config['name']] = {
                    'model': model,
                    'order': order,
                    'aic': aic,
                    'bic': model.bic,
                    'results': results
                }

                print(f"✅ {config['name']}: ARIMA{order}, AIC: {aic:.2f}")

                if aic < best_aic:
                    best_aic = aic
                    best_model = model
                    best_order = order
                    best_config = config['name']
            else:
                print(f"❌ {config['name']} configuration failed")

        except Exception as e:
            print(f"❌ {config['name']} configuration failed: {str(e)}")

    if best_model:
        print(f"\n🏆 Best Configuration: {best_config}")
        print(f"🏆 Best Model: ARIMA{best_order}")
        print(f"🏆 Best AIC: {best_aic:.2f}")

    return best_model, best_order, best_config, all_results

def main():
    """Main grid search optimization pipeline"""
    print("Starting Grid Search parameter optimization...")

    # Load data
    data, preprocessing_params = load_preprocessed_data()

    # Split data
    train_data, test_data = split_data_for_optimization(data)
    train_series = train_data['ProcessedRevenue']
    test_series = test_data['ProcessedRevenue']

    # Run multiple grid search configurations
    best_model, best_order, best_config, config_results = run_multiple_grid_search_configurations(train_series)

    if best_model is None:
        print("❌ All grid search configurations failed")
        return None

    # Evaluate best model
    performance = evaluate_optimized_model(best_model, test_series, best_order)

    if performance is None:
        print("❌ Model evaluation failed")
        return None

    # Compare with baseline
    comparison = compare_with_baseline(performance)

    # Create visualization
    create_auto_arima_visualization(train_data, test_data,
                                  performance['forecasts'],
                                  performance['confidence_intervals'],
                                  best_order)

    # Save results
    results = {
        'best_model_order': best_order,
        'best_configuration': best_config,
        'model_aic': best_model.aic,
        'model_bic': best_model.bic,
        'performance_metrics': {
            'mae': performance['mae'],
            'rmse': performance['rmse'],
            'mape': performance['mape'],
            'mae_percentage': performance['mae_percentage'],
            'rmse_percentage': performance['rmse_percentage'],
            'meets_targets': performance['overall_pass']
        },
        'configuration_comparison': {name: {'order': res['order'], 'aic': res['aic'], 'bic': res['bic']}
                                   for name, res in config_results.items() if res is not None},
        'baseline_comparison': comparison,
        'preprocessing_params': preprocessing_params
    }

    with open('grid_search_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)

    # Final summary
    print(f"\n{'='*60}")
    print("GRID SEARCH OPTIMIZATION SUMMARY")
    print(f"{'='*60}")
    print(f"🏆 Best Model: ARIMA{best_order} ({best_config} config)")
    print(f"📊 Performance:")
    print(f"   MAE: {performance['mae']:.4f} ({performance['mae_percentage']:.2f}%)")
    print(f"   RMSE: {performance['rmse']:.4f} ({performance['rmse_percentage']:.2f}%)")
    print(f"   MAPE: {performance['mape']:.2f}%")
    print(f"🎯 Accuracy Targets: {'✅ MET' if performance['overall_pass'] else '❌ NOT MET'}")

    if comparison and comparison['auto_arima_better']:
        print(f"🚀 Grid Search shows improvement over baseline models")

    print(f"\n✅ Results saved to: grid_search_results.json")

    return results

if __name__ == "__main__":
    results = main()
