#!/usr/bin/env python3
"""
Dataset Evaluation Summary
This script generates a comprehensive evaluation summary of the cleaned dataset.
"""

import json
import pandas as pd
from datetime import datetime

def load_evaluation_results():
    """Load all evaluation results"""
    with open('data_evaluation_results.json', 'r') as f:
        results = json.load(f)
    
    daily_ts = pd.read_csv('daily_revenue_timeseries.csv')
    daily_ts['Date'] = pd.to_datetime(daily_ts['Date'])
    
    return results, daily_ts

def generate_executive_summary(results, daily_ts):
    """Generate executive summary of dataset evaluation"""
    
    print("="*80)
    print("DATASET EVALUATION EXECUTIVE SUMMARY")
    print("="*80)
    print(f"Evaluation Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Dataset: Transaction Data for Time Series Forecasting")
    
    print("\n📊 OVERALL ASSESSMENT")
    print("-" * 40)
    score = results['data_quality_scores']['total']
    grade = results['data_quality_grade']
    print(f"Data Quality Score: {score:.1f}/100")
    print(f"Data Quality Grade: {grade}")
    
    if score >= 90:
        recommendation = "✅ EXCELLENT - Dataset is ready for advanced ARIMA modeling"
    elif score >= 80:
        recommendation = "✅ VERY GOOD - Dataset suitable for ARIMA modeling with minor preprocessing"
    elif score >= 70:
        recommendation = "⚠️  GOOD - Dataset usable but may need additional preprocessing"
    else:
        recommendation = "❌ NEEDS IMPROVEMENT - Significant data quality issues to address"
    
    print(f"Recommendation: {recommendation}")
    
    print("\n📈 TIME SERIES CHARACTERISTICS")
    print("-" * 40)
    print(f"Time Series Length: {results['time_series_length']} days")
    print(f"Date Range: {results['date_range']['start'][:10]} to {results['date_range']['end'][:10]}")
    print(f"Average Daily Revenue: ${results['business_metrics']['avg_daily_revenue']:,.2f}")
    print(f"Revenue Variability (CV): {(daily_ts['DailyRevenue'].std()/daily_ts['DailyRevenue'].mean()):.1%}")
    
    # Stationarity assessment
    stationarity = results['stationarity_analysis']
    print(f"\n🔍 STATIONARITY ANALYSIS")
    print("-" * 40)
    print(f"Trend Change: {stationarity['trend_change_pct']:.1f}%")
    print(f"Variance Change: {stationarity['variance_change_pct']:.1f}%")
    
    if stationarity['needs_differencing']:
        print("⚠️  Recommendation: Apply differencing to remove trend")
    else:
        print("✅ Trend: Series appears stationary")
        
    if stationarity['needs_transformation']:
        print("⚠️  Recommendation: Consider log transformation for variance stabilization")
    else:
        print("✅ Variance: Series variance appears stable")
    
    print(f"\n💼 BUSINESS INSIGHTS")
    print("-" * 40)
    metrics = results['business_metrics']
    print(f"Total Revenue: ${metrics['total_revenue']:,.2f}")
    print(f"Unique Customers: {metrics['total_customers']:,}")
    print(f"Unique Products: {metrics['total_products']:,}")
    print(f"Geographic Markets: {metrics['total_countries']}")
    
    return score, grade

def detailed_score_breakdown(results):
    """Provide detailed breakdown of quality scores"""
    
    print(f"\n📋 DETAILED QUALITY SCORE BREAKDOWN")
    print("-" * 50)
    
    scores = results['data_quality_scores']
    
    categories = [
        ('Completeness', scores['completeness'], 25, "Data completeness and missing value handling"),
        ('Consistency', scores['consistency'], 20, "Data type consistency and value ranges"),
        ('Time Series Quality', scores['time_series'], 25, "Time series structure and adequacy"),
        ('Business Relevance', scores['business_relevance'], 15, "Business value and forecasting suitability"),
        ('Data Richness', scores['richness'], 15, "Dimensional diversity and analytical depth")
    ]
    
    for category, score, max_score, description in categories:
        percentage = (score / max_score) * 100
        bar_length = int(percentage / 5)  # Scale to 20 characters
        bar = "█" * bar_length + "░" * (20 - bar_length)
        
        print(f"{category:<20} {score:>5.1f}/{max_score:<2} [{bar}] {percentage:>5.1f}%")
        print(f"{'':>20} {description}")
        print()

def arima_modeling_readiness(results, daily_ts):
    """Assess readiness for ARIMA modeling"""
    
    print(f"\n🎯 ARIMA MODELING READINESS ASSESSMENT")
    print("-" * 50)
    
    readiness_score = 0
    max_readiness = 100
    
    # 1. Data Length (20 points)
    length_score = 0
    ts_length = len(daily_ts)
    if ts_length >= 300:
        length_score = 20
    elif ts_length >= 200:
        length_score = 16
    elif ts_length >= 100:
        length_score = 12
    else:
        length_score = 6
    
    readiness_score += length_score
    print(f"Data Length ({ts_length} days): {length_score}/20 points")
    
    # 2. Stationarity (25 points)
    stationarity = results['stationarity_analysis']
    stationarity_score = 25
    if stationarity['needs_differencing']:
        stationarity_score -= 8
    if stationarity['needs_transformation']:
        stationarity_score -= 7
    
    readiness_score += stationarity_score
    print(f"Stationarity: {stationarity_score}/25 points")
    
    # 3. Seasonality Detection (15 points)
    # Based on coefficient of variation in monthly/weekly patterns
    revenue = daily_ts['DailyRevenue']
    monthly_cv = daily_ts.groupby(daily_ts['Date'].dt.month)['DailyRevenue'].mean().std() / daily_ts.groupby(daily_ts['Date'].dt.month)['DailyRevenue'].mean().mean()
    
    seasonality_score = 15
    if monthly_cv > 0.3:  # Strong seasonality
        seasonality_score = 15
    elif monthly_cv > 0.2:  # Moderate seasonality
        seasonality_score = 12
    else:  # Weak seasonality
        seasonality_score = 8
    
    readiness_score += seasonality_score
    print(f"Seasonality Patterns: {seasonality_score}/15 points")
    
    # 4. Data Quality (25 points)
    quality_score = min(25, results['data_quality_scores']['total'] * 0.25)
    readiness_score += quality_score
    print(f"Data Quality: {quality_score:.1f}/25 points")
    
    # 5. Business Relevance (15 points)
    business_score = results['data_quality_scores']['business_relevance']
    readiness_score += business_score
    print(f"Business Relevance: {business_score}/15 points")
    
    print(f"\n🎯 TOTAL ARIMA READINESS SCORE: {readiness_score:.1f}/100")
    
    if readiness_score >= 85:
        arima_grade = "A+ (Excellent - Ready for advanced SARIMA/SARIMAX)"
        recommendations = [
            "✅ Proceed with SARIMA modeling including seasonal components",
            "✅ Consider SARIMAX with external variables",
            "✅ Implement automated parameter selection (auto_arima)",
            "✅ Use cross-validation for robust model evaluation"
        ]
    elif readiness_score >= 75:
        arima_grade = "A (Very Good - Ready for ARIMA/SARIMA)"
        recommendations = [
            "✅ Start with basic ARIMA modeling",
            "✅ Test seasonal components (SARIMA)",
            "⚠️  Apply recommended preprocessing steps",
            "✅ Use grid search for parameter optimization"
        ]
    elif readiness_score >= 65:
        arima_grade = "B (Good - Needs preprocessing)"
        recommendations = [
            "⚠️  Apply differencing and/or transformation first",
            "⚠️  Consider data augmentation techniques",
            "✅ Start with simple ARIMA models",
            "⚠️  Monitor model diagnostics carefully"
        ]
    else:
        arima_grade = "C (Needs significant work)"
        recommendations = [
            "❌ Address data quality issues first",
            "❌ Consider collecting more data",
            "❌ Review data collection process",
            "⚠️  Start with simple forecasting methods"
        ]
    
    print(f"ARIMA Readiness Grade: {arima_grade}")
    
    print(f"\n📝 MODELING RECOMMENDATIONS:")
    for rec in recommendations:
        print(f"   {rec}")
    
    return readiness_score, arima_grade, recommendations

def generate_next_steps(results, arima_readiness_score):
    """Generate recommended next steps"""
    
    print(f"\n🚀 RECOMMENDED NEXT STEPS")
    print("-" * 40)
    
    if arima_readiness_score >= 75:
        steps = [
            "1. 🎯 Proceed to ARIMA model development",
            "2. 📊 Implement automated parameter selection (auto_arima)",
            "3. 🔄 Set up train/validation/test splits",
            "4. 📈 Build baseline ARIMA models",
            "5. 🌟 Experiment with SARIMA for seasonality",
            "6. 🔍 Evaluate model performance (MAE, MAPE, RMSE)",
            "7. 🚀 Deploy best model as API using Flask"
        ]
    else:
        steps = [
            "1. ⚠️  Apply recommended preprocessing steps",
            "2. 🔄 Re-evaluate data quality after preprocessing",
            "3. 📊 Consider data augmentation or collection",
            "4. 🎯 Start with simple ARIMA models",
            "5. 📈 Monitor model diagnostics closely",
            "6. 🔍 Iterate on data preparation as needed",
            "7. 🚀 Proceed to deployment only after validation"
        ]
    
    for step in steps:
        print(f"   {step}")

def main():
    """Main evaluation summary"""
    print("Generating comprehensive dataset evaluation summary...")
    
    # Load results
    results, daily_ts = load_evaluation_results()
    
    # Generate summary sections
    overall_score, grade = generate_executive_summary(results, daily_ts)
    detailed_score_breakdown(results)
    arima_score, arima_grade, recommendations = arima_modeling_readiness(results, daily_ts)
    generate_next_steps(results, arima_score)
    
    # Final summary
    print(f"\n" + "="*80)
    print("FINAL EVALUATION SUMMARY")
    print("="*80)
    print(f"📊 Dataset Quality Score: {overall_score:.1f}/100 ({grade})")
    print(f"🎯 ARIMA Readiness Score: {arima_score:.1f}/100 ({arima_grade})")
    
    if overall_score >= 85 and arima_score >= 75:
        final_recommendation = "🚀 PROCEED TO ARIMA MODEL DEVELOPMENT"
    elif overall_score >= 70 or arima_score >= 65:
        final_recommendation = "⚠️  APPLY PREPROCESSING THEN PROCEED"
    else:
        final_recommendation = "❌ ADDRESS DATA QUALITY ISSUES FIRST"
    
    print(f"🎯 Final Recommendation: {final_recommendation}")
    
    # Save summary
    summary_data = {
        'evaluation_timestamp': datetime.now().isoformat(),
        'overall_quality_score': overall_score,
        'overall_quality_grade': grade,
        'arima_readiness_score': arima_score,
        'arima_readiness_grade': arima_grade,
        'final_recommendation': final_recommendation,
        'modeling_recommendations': recommendations
    }
    
    with open('dataset_evaluation_summary.json', 'w') as f:
        json.dump(summary_data, f, indent=2)
    
    print(f"\n✅ Evaluation summary saved to: dataset_evaluation_summary.json")
    
    return summary_data

if __name__ == "__main__":
    summary = main()
