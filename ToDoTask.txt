// Datasets
1. Clean the dataset for a better quality using python and its library MUST be using pandas
2. Tidy and evaluate the cleaned datasets
3. Dataset evaluation score

// ARIMA Models
1. Building a High Quality ARIMA or SARIMA or SARIMAX model based on the end results from the dataset evaluation
2. Training the time series model, using the best method and algorithm based on dataset evaluation
3. Configuring the model for forecasting financial data, revenue data, etc.
4. Evaluating its P, D, Q, s parameters
5. Evaluating the model's performance
6. Evaluating the model's accuracy (MAE and MAPE <5%; RMSE <10%)
7. Evaluating the model's forecast
8. Evaluating the model's confidence interval
9. Evaluating the model's residuals
10. Evaluating the model's autocorrelation
11. Evaluating the model's partial autocorrelation
12. Evaluating the model's seasonality
13. Evaluating the model's stationarity
14. Evaluate the model's performance for system resource usage
15. Model evaluation score
16. Deploy as an API using flask

// Web App
1. Building a web app with time series choosing (tomorrow with hourly chart, next week with daily chart, next month with weekly chart, next year with monthly chart)
2. Adding custom time series choosing (4 days, 3 months, 8 weeks, 4 years)
3. Adding a chart for the forecasted data
4. Building a clean and modern UI
5. Evaluate the web app
6. Web app evaluation score
7. Deploy the web app using flask