#!/usr/bin/env python3
"""
Baseline ARIMA Models Script
This script builds and evaluates baseline ARIMA models with manual parameter selection.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import json
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.stattools import acf, pacf
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from sklearn.metrics import mean_absolute_error, mean_squared_error
import warnings
warnings.filterwarnings('ignore')

def load_preprocessed_data():
    """Load preprocessed time series data and parameters"""
    print("Loading preprocessed data...")
    
    # Load preprocessed time series
    data = pd.read_csv('preprocessed_timeseries.csv')
    data['Date'] = pd.to_datetime(data['Date'])
    
    # Load preprocessing parameters
    with open('preprocessing_parameters.json', 'r') as f:
        params = json.load(f)
    
    print(f"Loaded {len(data)} preprocessed observations")
    print(f"Preprocessing: {params['transformation_note']}, d={params['differencing_order']}")
    
    return data, params

def analyze_acf_pacf(series, max_lags=40):
    """Analyze ACF and PACF to suggest ARIMA parameters"""
    print(f"\n{'='*50}")
    print("ACF/PACF ANALYSIS FOR PARAMETER SELECTION")
    print(f"{'='*50}")
    
    # Calculate ACF and PACF
    max_lags = min(max_lags, len(series) // 4)
    acf_values = acf(series.dropna(), nlags=max_lags, alpha=0.05)
    pacf_values = pacf(series.dropna(), nlags=max_lags, alpha=0.05)
    
    # Analyze ACF for MA order (q)
    acf_cutoff = None
    for i in range(1, len(acf_values[0])):
        if abs(acf_values[0][i]) < 0.05:  # Below significance threshold
            acf_cutoff = i
            break
    
    # Analyze PACF for AR order (p)
    pacf_cutoff = None
    for i in range(1, len(pacf_values[0])):
        if abs(pacf_values[0][i]) < 0.05:  # Below significance threshold
            pacf_cutoff = i
            break
    
    # Suggest parameters
    suggested_p = min(pacf_cutoff or 3, 5) if pacf_cutoff else 2
    suggested_q = min(acf_cutoff or 3, 5) if acf_cutoff else 2
    
    print(f"ACF analysis suggests MA order (q): {suggested_q}")
    print(f"PACF analysis suggests AR order (p): {suggested_p}")
    
    # Create ACF/PACF plots
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    plot_acf(series.dropna(), lags=max_lags, ax=ax1, alpha=0.05)
    ax1.set_title('Autocorrelation Function (ACF)')
    
    plot_pacf(series.dropna(), lags=max_lags, ax=ax2, alpha=0.05)
    ax2.set_title('Partial Autocorrelation Function (PACF)')
    
    plt.tight_layout()
    plt.savefig('acf_pacf_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ ACF/PACF plots saved to: acf_pacf_analysis.png")
    
    return suggested_p, suggested_q

def split_train_test(data, test_size=0.2):
    """Split data into train and test sets"""
    n_test = int(len(data) * test_size)
    n_train = len(data) - n_test
    
    train_data = data.iloc[:n_train].copy()
    test_data = data.iloc[n_train:].copy()
    
    print(f"\nData split:")
    print(f"Training set: {len(train_data)} observations ({train_data['Date'].min()} to {train_data['Date'].max()})")
    print(f"Test set: {len(test_data)} observations ({test_data['Date'].min()} to {test_data['Date'].max()})")
    
    return train_data, test_data

def fit_arima_model(train_series, order, name="ARIMA"):
    """Fit ARIMA model and return results"""
    print(f"\nFitting {name}{order}...")
    
    try:
        model = ARIMA(train_series, order=order)
        fitted_model = model.fit()
        
        # Model diagnostics
        aic = fitted_model.aic
        bic = fitted_model.bic
        log_likelihood = fitted_model.llf
        
        print(f"✅ {name}{order} fitted successfully")
        print(f"   AIC: {aic:.2f}")
        print(f"   BIC: {bic:.2f}")
        print(f"   Log-likelihood: {log_likelihood:.2f}")
        
        return fitted_model, {
            'order': order,
            'aic': aic,
            'bic': bic,
            'log_likelihood': log_likelihood,
            'converged': fitted_model.mle_retvals['converged'] if hasattr(fitted_model, 'mle_retvals') else True
        }
        
    except Exception as e:
        print(f"❌ Failed to fit {name}{order}: {str(e)}")
        return None, None

def evaluate_model_performance(model, train_series, test_series, model_name="Model"):
    """Evaluate model performance on test set"""
    print(f"\nEvaluating {model_name} performance...")
    
    try:
        # Generate forecasts
        n_forecast = len(test_series)
        forecast_result = model.forecast(steps=n_forecast)
        
        if hasattr(forecast_result, 'predicted_mean'):
            forecasts = forecast_result.predicted_mean
            conf_int = forecast_result.conf_int()
        else:
            forecasts = forecast_result
            conf_int = None
        
        # Calculate metrics
        mae = mean_absolute_error(test_series, forecasts)
        rmse = np.sqrt(mean_squared_error(test_series, forecasts))
        mape = np.mean(np.abs((test_series - forecasts) / test_series)) * 100
        
        # Calculate additional metrics
        mean_actual = np.mean(test_series)
        mae_percentage = (mae / mean_actual) * 100
        rmse_percentage = (rmse / mean_actual) * 100
        
        print(f"📊 Performance Metrics for {model_name}:")
        print(f"   MAE: {mae:.4f} ({mae_percentage:.2f}% of mean)")
        print(f"   RMSE: {rmse:.4f} ({rmse_percentage:.2f}% of mean)")
        print(f"   MAPE: {mape:.2f}%")
        
        # Check if meets accuracy targets
        meets_mape_target = mape < 5.0
        meets_rmse_target = rmse_percentage < 10.0
        
        print(f"   MAPE Target (<5%): {'✅ PASS' if meets_mape_target else '❌ FAIL'}")
        print(f"   RMSE Target (<10%): {'✅ PASS' if meets_rmse_target else '❌ FAIL'}")
        
        return {
            'forecasts': forecasts,
            'confidence_intervals': conf_int,
            'mae': mae,
            'rmse': rmse,
            'mape': mape,
            'mae_percentage': mae_percentage,
            'rmse_percentage': rmse_percentage,
            'meets_mape_target': meets_mape_target,
            'meets_rmse_target': meets_rmse_target,
            'overall_pass': meets_mape_target and meets_rmse_target
        }
        
    except Exception as e:
        print(f"❌ Error evaluating {model_name}: {str(e)}")
        return None

def build_baseline_models(train_series, suggested_p, suggested_q, d=1):
    """Build multiple baseline ARIMA models"""
    print(f"\n{'='*60}")
    print("BUILDING BASELINE ARIMA MODELS")
    print(f"{'='*60}")
    
    # Define candidate models based on ACF/PACF analysis
    candidate_orders = [
        (0, d, 0),  # Random walk with drift
        (1, d, 0),  # AR(1)
        (0, d, 1),  # MA(1)
        (1, d, 1),  # ARMA(1,1)
        (suggested_p, d, 0),  # AR based on PACF
        (0, d, suggested_q),  # MA based on ACF
        (suggested_p, d, suggested_q),  # ARMA based on both
        (2, d, 2),  # ARMA(2,2)
    ]
    
    # Remove duplicates
    candidate_orders = list(set(candidate_orders))
    
    models = {}
    model_info = {}
    
    print(f"Testing {len(candidate_orders)} candidate models...")
    
    for order in candidate_orders:
        model_name = f"ARIMA{order}"
        fitted_model, info = fit_arima_model(train_series, order, "ARIMA")
        
        if fitted_model is not None:
            models[model_name] = fitted_model
            model_info[model_name] = info
    
    print(f"\n✅ Successfully fitted {len(models)} models")
    
    return models, model_info

def select_best_model(model_info):
    """Select best model based on information criteria"""
    print(f"\n{'='*50}")
    print("MODEL SELECTION")
    print(f"{'='*50}")
    
    if not model_info:
        print("❌ No models to compare")
        return None
    
    # Create comparison table
    comparison_data = []
    for name, info in model_info.items():
        comparison_data.append({
            'Model': name,
            'AIC': info['aic'],
            'BIC': info['bic'],
            'Log-Likelihood': info['log_likelihood']
        })
    
    comparison_df = pd.DataFrame(comparison_data)
    comparison_df = comparison_df.sort_values('AIC')
    
    print("Model Comparison (sorted by AIC):")
    print(comparison_df.to_string(index=False, float_format='%.2f'))
    
    # Select best model
    best_model_name = comparison_df.iloc[0]['Model']
    best_aic = comparison_df.iloc[0]['AIC']
    
    print(f"\n🏆 Best Model: {best_model_name} (AIC: {best_aic:.2f})")
    
    return best_model_name, comparison_df

def create_forecast_visualization(train_data, test_data, forecasts, conf_int=None, model_name="ARIMA"):
    """Create visualization of forecasts vs actual"""
    print(f"\nCreating forecast visualization for {model_name}...")
    
    fig, ax = plt.subplots(figsize=(15, 8))
    
    # Plot training data
    ax.plot(train_data['Date'], train_data['ProcessedRevenue'], 
            label='Training Data', color='blue', alpha=0.7)
    
    # Plot test data (actual)
    ax.plot(test_data['Date'], test_data['ProcessedRevenue'], 
            label='Actual (Test)', color='green', linewidth=2)
    
    # Plot forecasts
    ax.plot(test_data['Date'], forecasts, 
            label=f'{model_name} Forecast', color='red', linewidth=2, linestyle='--')
    
    # Plot confidence intervals if available
    if conf_int is not None:
        ax.fill_between(test_data['Date'], 
                       conf_int.iloc[:, 0], conf_int.iloc[:, 1],
                       color='red', alpha=0.2, label='95% Confidence Interval')
    
    ax.set_title(f'{model_name} Forecast vs Actual')
    ax.set_xlabel('Date')
    ax.set_ylabel('Processed Revenue (Log-Differenced)')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(f'{model_name.lower()}_forecast_visualization.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ Forecast visualization saved to: {model_name.lower()}_forecast_visualization.png")

def main():
    """Main baseline ARIMA modeling pipeline"""
    print("Starting baseline ARIMA modeling pipeline...")
    
    # Load data
    data, preprocessing_params = load_preprocessed_data()
    
    # Analyze ACF/PACF for parameter suggestions
    processed_series = data['ProcessedRevenue']
    suggested_p, suggested_q = analyze_acf_pacf(processed_series)
    
    # Split data
    train_data, test_data = split_train_test(data)
    train_series = train_data['ProcessedRevenue']
    test_series = test_data['ProcessedRevenue']
    
    # Build baseline models
    d = preprocessing_params['differencing_order']
    models, model_info = build_baseline_models(train_series, suggested_p, suggested_q, d)
    
    if not models:
        print("❌ No models were successfully fitted")
        return None
    
    # Select best model
    best_model_name, comparison_df = select_best_model(model_info)
    best_model = models[best_model_name]
    
    # Evaluate best model
    performance = evaluate_model_performance(best_model, train_series, test_series, best_model_name)
    
    if performance:
        # Create visualization
        create_forecast_visualization(train_data, test_data, 
                                    performance['forecasts'], 
                                    performance['confidence_intervals'],
                                    best_model_name)
        
        # Save results
        results = {
            'best_model': best_model_name,
            'model_comparison': comparison_df.to_dict('records'),
            'performance_metrics': {
                'mae': performance['mae'],
                'rmse': performance['rmse'],
                'mape': performance['mape'],
                'mae_percentage': performance['mae_percentage'],
                'rmse_percentage': performance['rmse_percentage'],
                'meets_targets': performance['overall_pass']
            },
            'preprocessing_params': preprocessing_params,
            'suggested_parameters': {
                'p': suggested_p,
                'q': suggested_q,
                'd': d
            }
        }
        
        with open('baseline_arima_results.json', 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        # Summary
        print(f"\n{'='*60}")
        print("BASELINE ARIMA MODELING SUMMARY")
        print(f"{'='*60}")
        print(f"🏆 Best Model: {best_model_name}")
        print(f"📊 Performance:")
        print(f"   MAE: {performance['mae']:.4f} ({performance['mae_percentage']:.2f}%)")
        print(f"   RMSE: {performance['rmse']:.4f} ({performance['rmse_percentage']:.2f}%)")
        print(f"   MAPE: {performance['mape']:.2f}%")
        print(f"🎯 Accuracy Targets: {'✅ MET' if performance['overall_pass'] else '❌ NOT MET'}")
        
        if not performance['overall_pass']:
            print("\n⚠️ Recommendations:")
            if not performance['meets_mape_target']:
                print("   - MAPE > 5%: Consider auto_arima for better parameter selection")
            if not performance['meets_rmse_target']:
                print("   - RMSE > 10%: Consider seasonal models or external variables")
        
        print(f"\n✅ Results saved to: baseline_arima_results.json")
        
        return results
    
    else:
        print("❌ Model evaluation failed")
        return None

if __name__ == "__main__":
    results = main()
