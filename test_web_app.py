#!/usr/bin/env python3
"""
Simple test version of the web application to identify issues
"""

from flask import Flask, render_template, request, jsonify
import pandas as pd
import numpy as np
import json
from datetime import datetime, timedelta
from statsmodels.tsa.arima.model import ARIMA

app = Flask(__name__)
app.secret_key = 'test_key'

# Global variables
model = None
model_info = None
historical_data = None

def load_test_data():
    """Load minimal data for testing"""
    global model, model_info, historical_data
    
    try:
        print("Loading test data...")
        
        # Load basic model info
        with open('model_evaluation_summary.json', 'r') as f:
            evaluation = json.load(f)
        model_info = evaluation['best_model']
        
        # Load minimal historical data
        data = pd.read_csv('preprocessed_timeseries.csv')
        data['Date'] = pd.to_datetime(data['Date'])
        historical_data = data.tail(50)  # Just last 50 points
        
        # Create a simple mock model for testing
        train_series = data['ProcessedRevenue'].tail(100)
        arima_model = ARIMA(train_series, order=(1,0,1))
        model = arima_model.fit()
        
        print("✅ Test data loaded successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error loading test data: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

@app.route('/')
def home():
    """Simple home page"""
    if model is None:
        return "Model not loaded"
    
    return f"""
    <html>
    <head><title>Test Web App</title></head>
    <body>
        <h1>Time Series Forecasting Test</h1>
        <p>Model: {model_info['Model']}</p>
        <p>MAPE: {model_info['MAPE']:.2f}%</p>
        <p>Data points: {len(historical_data)}</p>
        <a href="/test-forecast">Test Forecast</a>
    </body>
    </html>
    """

@app.route('/test-forecast')
def test_forecast():
    """Simple forecast test"""
    if model is None:
        return "Model not loaded"
    
    try:
        # Generate a simple 7-day forecast
        forecast_result = model.get_forecast(steps=7)
        forecasts = forecast_result.predicted_mean.values
        
        # Apply inverse transformation (exp if log transformed)
        forecasts_original = np.exp(forecasts)
        
        return f"""
        <html>
        <head><title>Test Forecast</title></head>
        <body>
            <h1>Test Forecast Results</h1>
            <p>7-day forecast:</p>
            <ul>
            {''.join([f'<li>Day {i+1}: ${val:.2f}</li>' for i, val in enumerate(forecasts_original)])}
            </ul>
            <a href="/">Back to Home</a>
        </body>
        </html>
        """
        
    except Exception as e:
        return f"Forecast error: {str(e)}"

@app.route('/api/test')
def api_test():
    """Simple API test"""
    return jsonify({
        'status': 'ok',
        'model_loaded': model is not None,
        'data_points': len(historical_data) if historical_data is not None else 0
    })

if __name__ == '__main__':
    print("Starting Test Web Application...")
    
    if load_test_data():
        print("✅ Starting Flask server on port 5003...")
        app.run(host='0.0.0.0', port=5003, debug=True)
    else:
        print("❌ Failed to load test data")
