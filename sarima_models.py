#!/usr/bin/env python3
"""
SARIMA Models Script
This script implements seasonal ARIMA models to capture seasonal patterns.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import json
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller
from sklearn.metrics import mean_absolute_error, mean_squared_error
from itertools import product
import warnings
warnings.filterwarnings('ignore')

def load_data_for_sarima():
    """Load data for SARIMA modeling"""
    print("Loading data for SARIMA modeling...")
    
    # Load original daily time series (before differencing for seasonal analysis)
    daily_ts = pd.read_csv('daily_revenue_timeseries.csv')
    daily_ts['Date'] = pd.to_datetime(daily_ts['Date'])
    daily_ts = daily_ts.sort_values('Date').reset_index(drop=True)
    
    # Load preprocessing parameters
    with open('preprocessing_parameters.json', 'r') as f:
        preprocessing_params = json.load(f)
    
    print(f"Loaded {len(daily_ts)} daily observations")
    print(f"Date range: {daily_ts['Date'].min()} to {daily_ts['Date'].max()}")
    
    return daily_ts, preprocessing_params

def perform_seasonal_decomposition(series, period=7):
    """Perform seasonal decomposition to analyze seasonal patterns"""
    print(f"\n{'='*50}")
    print(f"SEASONAL DECOMPOSITION (Period={period})")
    print(f"{'='*50}")
    
    try:
        # Perform seasonal decomposition
        decomposition = seasonal_decompose(series, model='additive', period=period)
        
        # Calculate seasonal strength
        seasonal_var = np.var(decomposition.seasonal)
        residual_var = np.var(decomposition.resid.dropna())
        seasonal_strength = seasonal_var / (seasonal_var + residual_var)
        
        print(f"Seasonal strength: {seasonal_strength:.4f}")
        
        # Create decomposition plot
        fig, axes = plt.subplots(4, 1, figsize=(15, 12))
        
        decomposition.observed.plot(ax=axes[0], title='Original')
        decomposition.trend.plot(ax=axes[1], title='Trend')
        decomposition.seasonal.plot(ax=axes[2], title='Seasonal')
        decomposition.resid.plot(ax=axes[3], title='Residual')
        
        plt.tight_layout()
        plt.savefig(f'seasonal_decomposition_period_{period}.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ Seasonal decomposition plot saved: seasonal_decomposition_period_{period}.png")
        
        return {
            'seasonal_strength': seasonal_strength,
            'seasonal_component': decomposition.seasonal,
            'trend_component': decomposition.trend,
            'residual_component': decomposition.resid,
            'is_seasonal': seasonal_strength > 0.1
        }
        
    except Exception as e:
        print(f"❌ Seasonal decomposition failed: {str(e)}")
        return None

def test_multiple_seasonal_periods(series):
    """Test multiple seasonal periods to find the best one"""
    print(f"\n{'='*60}")
    print("TESTING MULTIPLE SEASONAL PERIODS")
    print(f"{'='*60}")
    
    # Test different seasonal periods
    periods_to_test = [7, 14, 30, 90]  # Weekly, bi-weekly, monthly, quarterly
    seasonal_results = {}
    
    for period in periods_to_test:
        if len(series) >= 2 * period:  # Need at least 2 full cycles
            print(f"\nTesting seasonal period: {period}")
            result = perform_seasonal_decomposition(series, period)
            if result:
                seasonal_results[period] = result
                print(f"Period {period}: Seasonal strength = {result['seasonal_strength']:.4f}")
        else:
            print(f"Period {period}: Insufficient data (need {2*period}, have {len(series)})")
    
    # Find best seasonal period
    if seasonal_results:
        best_period = max(seasonal_results.keys(), 
                         key=lambda k: seasonal_results[k]['seasonal_strength'])
        best_strength = seasonal_results[best_period]['seasonal_strength']
        
        print(f"\n🏆 Best seasonal period: {best_period} (strength: {best_strength:.4f})")
        
        if best_strength > 0.1:
            print("✅ Strong seasonality detected - SARIMA recommended")
            return best_period, seasonal_results
        else:
            print("⚠️ Weak seasonality - Simple ARIMA may be sufficient")
            return None, seasonal_results
    else:
        print("❌ No valid seasonal decompositions found")
        return None, {}

def build_sarima_models(train_series, seasonal_period=7, max_p=3, max_q=3, max_P=2, max_Q=2):
    """Build SARIMA models with different parameter combinations"""
    print(f"\n{'='*60}")
    print("BUILDING SARIMA MODELS")
    print(f"{'='*60}")
    
    print(f"Seasonal period: {seasonal_period}")
    print(f"Search space: p(0-{max_p}), q(0-{max_q}), P(0-{max_P}), Q(0-{max_Q})")
    
    # Generate parameter combinations
    p_values = range(0, max_p + 1)
    q_values = range(0, max_q + 1)
    P_values = range(0, max_P + 1)
    Q_values = range(0, max_Q + 1)
    
    # Fixed differencing orders based on preprocessing
    d = 1  # From preprocessing analysis
    D = 1  # Seasonal differencing
    
    best_aic = float('inf')
    best_order = None
    best_seasonal_order = None
    best_model = None
    results = []
    
    total_combinations = len(p_values) * len(q_values) * len(P_values) * len(Q_values)
    print(f"Testing {total_combinations} SARIMA parameter combinations...")
    
    for i, (p, q, P, Q) in enumerate(product(p_values, q_values, P_values, Q_values)):
        try:
            # Skip invalid combinations
            if p == 0 and q == 0 and P == 0 and Q == 0:
                continue
            
            order = (p, d, q)
            seasonal_order = (P, D, Q, seasonal_period)
            
            model = SARIMAX(train_series, 
                           order=order, 
                           seasonal_order=seasonal_order,
                           enforce_stationarity=False,
                           enforce_invertibility=False)
            fitted_model = model.fit(disp=False)
            
            aic = fitted_model.aic
            bic = fitted_model.bic
            
            results.append({
                'order': order,
                'seasonal_order': seasonal_order,
                'aic': aic,
                'bic': bic,
                'converged': fitted_model.mle_retvals['converged'] if hasattr(fitted_model, 'mle_retvals') else True
            })
            
            if aic < best_aic:
                best_aic = aic
                best_order = order
                best_seasonal_order = seasonal_order
                best_model = fitted_model
            
            if (i + 1) % 10 == 0:
                print(f"Progress: {i + 1}/{total_combinations} combinations tested")
                
        except Exception as e:
            # Skip problematic parameter combinations
            continue
    
    if best_model is not None:
        print(f"\n✅ SARIMA optimization completed!")
        print(f"🏆 Best model: SARIMA{best_order}x{best_seasonal_order}")
        print(f"📊 AIC: {best_aic:.2f}")
        print(f"📊 BIC: {best_model.bic:.2f}")
        
        return best_model, best_order, best_seasonal_order, results
    else:
        print(f"❌ SARIMA optimization failed - no valid models found")
        return None, None, None, []

def evaluate_sarima_model(fitted_model, test_series, order, seasonal_order):
    """Evaluate SARIMA model performance"""
    print(f"\n{'='*50}")
    print("SARIMA MODEL EVALUATION")
    print(f"{'='*50}")
    
    try:
        # Generate forecasts
        n_forecast = len(test_series)
        forecast_result = fitted_model.get_forecast(steps=n_forecast)
        forecasts = forecast_result.predicted_mean
        conf_int = forecast_result.conf_int()
        
        # Calculate metrics
        mae = mean_absolute_error(test_series, forecasts)
        rmse = np.sqrt(mean_squared_error(test_series, forecasts))
        mape = np.mean(np.abs((test_series - forecasts) / test_series)) * 100
        
        # Calculate percentage metrics
        mean_actual = np.mean(test_series)
        mae_percentage = (mae / abs(mean_actual)) * 100 if mean_actual != 0 else float('inf')
        rmse_percentage = (rmse / abs(mean_actual)) * 100 if mean_actual != 0 else float('inf')
        
        print(f"📊 SARIMA{order}x{seasonal_order} Performance Metrics:")
        print(f"   MAE: {mae:.4f} ({mae_percentage:.2f}% of mean)")
        print(f"   RMSE: {rmse:.4f} ({rmse_percentage:.2f}% of mean)")
        print(f"   MAPE: {mape:.2f}%")
        
        # Check accuracy targets
        meets_mape_target = mape < 5.0
        meets_rmse_target = rmse_percentage < 10.0
        
        print(f"   MAPE Target (<5%): {'✅ PASS' if meets_mape_target else '❌ FAIL'}")
        print(f"   RMSE Target (<10%): {'✅ PASS' if meets_rmse_target else '❌ FAIL'}")
        
        return {
            'forecasts': forecasts,
            'confidence_intervals': conf_int,
            'mae': mae,
            'rmse': rmse,
            'mape': mape,
            'mae_percentage': mae_percentage,
            'rmse_percentage': rmse_percentage,
            'meets_mape_target': meets_mape_target,
            'meets_rmse_target': meets_rmse_target,
            'overall_pass': meets_mape_target and meets_rmse_target
        }
        
    except Exception as e:
        print(f"❌ Error evaluating SARIMA model: {str(e)}")
        return None

def compare_sarima_with_arima():
    """Compare SARIMA performance with previous ARIMA models"""
    print(f"\n{'='*50}")
    print("SARIMA vs ARIMA COMPARISON")
    print(f"{'='*50}")
    
    comparison_data = []
    
    # Load baseline ARIMA results
    try:
        with open('baseline_arima_results.json', 'r') as f:
            baseline_results = json.load(f)
        comparison_data.append({
            'Model': baseline_results['best_model'],
            'MAE': baseline_results['performance_metrics']['mae'],
            'RMSE': baseline_results['performance_metrics']['rmse'],
            'MAPE': baseline_results['performance_metrics']['mape']
        })
    except FileNotFoundError:
        print("⚠️ Baseline ARIMA results not found")
    
    # Load grid search results
    try:
        with open('grid_search_results.json', 'r') as f:
            grid_results = json.load(f)
        comparison_data.append({
            'Model': f"ARIMA{grid_results['best_model_order']}",
            'MAE': grid_results['performance_metrics']['mae'],
            'RMSE': grid_results['performance_metrics']['rmse'],
            'MAPE': grid_results['performance_metrics']['mape']
        })
    except FileNotFoundError:
        print("⚠️ Grid search results not found")
    
    return comparison_data

def create_sarima_visualization(train_data, test_data, forecasts, conf_int, order, seasonal_order):
    """Create visualization for SARIMA results"""
    print(f"\nCreating SARIMA visualization...")
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))
    
    # Full time series plot
    ax1.plot(train_data['Date'], train_data['DailyRevenue'], 
             label='Training Data', color='blue', alpha=0.7)
    ax1.plot(test_data['Date'], test_data['DailyRevenue'], 
             label='Actual (Test)', color='green', linewidth=2)
    ax1.plot(test_data['Date'], forecasts, 
             label=f'SARIMA{order}x{seasonal_order} Forecast', color='red', linewidth=2, linestyle='--')
    
    # Confidence intervals
    if conf_int is not None:
        ax1.fill_between(test_data['Date'], 
                         conf_int.iloc[:, 0], conf_int.iloc[:, 1],
                         color='red', alpha=0.2, label='95% Confidence Interval')
    
    ax1.set_title(f'SARIMA{order}x{seasonal_order} - Full Time Series Forecast')
    ax1.set_ylabel('Daily Revenue ($)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Zoomed view of test period
    ax2.plot(test_data['Date'], test_data['DailyRevenue'], 
             label='Actual', color='green', linewidth=3, marker='o', markersize=4)
    ax2.plot(test_data['Date'], forecasts, 
             label=f'SARIMA{order}x{seasonal_order} Forecast', color='red', linewidth=3, 
             marker='s', markersize=4, linestyle='--')
    
    if conf_int is not None:
        ax2.fill_between(test_data['Date'], 
                         conf_int.iloc[:, 0], conf_int.iloc[:, 1],
                         color='red', alpha=0.2, label='95% Confidence Interval')
    
    ax2.set_title('Test Period - Detailed View')
    ax2.set_xlabel('Date')
    ax2.set_ylabel('Daily Revenue ($)')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('sarima_forecast_visualization.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ SARIMA visualization saved to: sarima_forecast_visualization.png")

def main():
    """Main SARIMA modeling pipeline"""
    print("Starting SARIMA modeling pipeline...")
    
    # Load data
    daily_ts, preprocessing_params = load_data_for_sarima()
    
    # Test seasonal patterns
    revenue_series = daily_ts['DailyRevenue']
    best_period, seasonal_results = test_multiple_seasonal_periods(revenue_series)
    
    if best_period is None:
        print("\n⚠️ No strong seasonal patterns detected")
        print("Recommendation: Use optimized ARIMA models instead of SARIMA")
        return None
    
    # Split data for SARIMA modeling
    n_test = int(len(daily_ts) * 0.2)
    train_data = daily_ts.iloc[:-n_test].copy()
    test_data = daily_ts.iloc[-n_test:].copy()
    
    print(f"\nData split for SARIMA:")
    print(f"Training: {len(train_data)} observations")
    print(f"Testing: {len(test_data)} observations")
    
    # Build SARIMA models
    train_series = train_data['DailyRevenue']
    test_series = test_data['DailyRevenue']
    
    best_model, best_order, best_seasonal_order, results = build_sarima_models(
        train_series, seasonal_period=best_period
    )
    
    if best_model is None:
        print("❌ SARIMA modeling failed")
        return None
    
    # Evaluate SARIMA model
    performance = evaluate_sarima_model(best_model, test_series, best_order, best_seasonal_order)
    
    if performance is None:
        print("❌ SARIMA evaluation failed")
        return None
    
    # Compare with ARIMA models
    arima_comparison = compare_sarima_with_arima()
    
    # Create visualization
    create_sarima_visualization(train_data, test_data, 
                              performance['forecasts'], 
                              performance['confidence_intervals'],
                              best_order, best_seasonal_order)
    
    # Save results
    sarima_results = {
        'best_model_order': best_order,
        'best_seasonal_order': best_seasonal_order,
        'seasonal_period': best_period,
        'model_aic': best_model.aic,
        'model_bic': best_model.bic,
        'performance_metrics': {
            'mae': performance['mae'],
            'rmse': performance['rmse'],
            'mape': performance['mape'],
            'mae_percentage': performance['mae_percentage'],
            'rmse_percentage': performance['rmse_percentage'],
            'meets_targets': performance['overall_pass']
        },
        'seasonal_analysis': {
            'best_period': best_period,
            'seasonal_strength': seasonal_results[best_period]['seasonal_strength'] if best_period in seasonal_results else 0
        },
        'arima_comparison': arima_comparison,
        'preprocessing_params': preprocessing_params
    }
    
    with open('sarima_results.json', 'w') as f:
        json.dump(sarima_results, f, indent=2, default=str)
    
    # Final summary
    print(f"\n{'='*60}")
    print("SARIMA MODELING SUMMARY")
    print(f"{'='*60}")
    print(f"🏆 Best Model: SARIMA{best_order}x{best_seasonal_order}")
    print(f"📊 Seasonal Period: {best_period}")
    print(f"📊 Performance:")
    print(f"   MAE: {performance['mae']:.4f} ({performance['mae_percentage']:.2f}%)")
    print(f"   RMSE: {performance['rmse']:.4f} ({performance['rmse_percentage']:.2f}%)")
    print(f"   MAPE: {performance['mape']:.2f}%")
    print(f"🎯 Accuracy Targets: {'✅ MET' if performance['overall_pass'] else '❌ NOT MET'}")
    
    print(f"\n✅ Results saved to: sarima_results.json")
    
    return sarima_results

if __name__ == "__main__":
    results = main()
