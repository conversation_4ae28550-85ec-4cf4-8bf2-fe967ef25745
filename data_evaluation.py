#!/usr/bin/env python3
"""
Data Evaluation and Analysis Script
This script performs comprehensive evaluation and analysis of the cleaned dataset.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('default')
plt.rcParams['figure.facecolor'] = 'white'

def load_cleaned_data():
    """Load the cleaned datasets"""
    print("Loading cleaned datasets...")
    
    df = pd.read_csv('cleaned_transaction_data.csv')
    daily_ts = pd.read_csv('daily_revenue_timeseries.csv')
    
    # Convert date columns
    df['TransactionTime'] = pd.to_datetime(df['TransactionTime'])
    df['Date'] = pd.to_datetime(df['Date'])
    daily_ts['Date'] = pd.to_datetime(daily_ts['Date'])
    
    print(f"Transaction data shape: {df.shape}")
    print(f"Time series data shape: {daily_ts.shape}")
    
    return df, daily_ts

def analyze_time_series_properties(daily_ts):
    """Analyze time series properties for ARIMA modeling"""
    print("\n" + "="*50)
    print("TIME SERIES ANALYSIS")
    print("="*50)
    
    # Basic time series statistics
    revenue = daily_ts['DailyRevenue']
    
    print(f"Time series length: {len(revenue)} days")
    print(f"Date range: {daily_ts['Date'].min().strftime('%Y-%m-%d')} to {daily_ts['Date'].max().strftime('%Y-%m-%d')}")
    print(f"Mean daily revenue: ${revenue.mean():.2f}")
    print(f"Standard deviation: ${revenue.std():.2f}")
    print(f"Coefficient of variation: {(revenue.std()/revenue.mean()*100):.2f}%")
    
    # Check for missing dates
    date_range = pd.date_range(start=daily_ts['Date'].min(), end=daily_ts['Date'].max(), freq='D')
    missing_dates = len(date_range) - len(daily_ts)
    print(f"Missing dates in series: {missing_dates}")
    
    # Trend analysis
    daily_ts['MA_7'] = revenue.rolling(window=7).mean()
    daily_ts['MA_30'] = revenue.rolling(window=30).mean()
    
    # Seasonality indicators
    daily_ts['Month'] = daily_ts['Date'].dt.month
    daily_ts['DayOfWeek'] = daily_ts['Date'].dt.dayofweek
    daily_ts['Quarter'] = daily_ts['Date'].dt.quarter
    
    # Monthly seasonality
    monthly_avg = daily_ts.groupby('Month')['DailyRevenue'].mean()
    print(f"\nMonthly seasonality (coefficient of variation): {(monthly_avg.std()/monthly_avg.mean()*100):.2f}%")
    
    # Weekly seasonality
    weekly_avg = daily_ts.groupby('DayOfWeek')['DailyRevenue'].mean()
    print(f"Weekly seasonality (coefficient of variation): {(weekly_avg.std()/weekly_avg.mean()*100):.2f}%")
    
    return daily_ts

def check_stationarity(daily_ts):
    """Check stationarity of the time series"""
    print("\n" + "="*30)
    print("STATIONARITY ANALYSIS")
    print("="*30)
    
    revenue = daily_ts['DailyRevenue']
    
    # Visual inspection of trend
    rolling_mean = revenue.rolling(window=30).mean()
    rolling_std = revenue.rolling(window=30).std()
    
    # Simple trend test
    first_half_mean = revenue[:len(revenue)//2].mean()
    second_half_mean = revenue[len(revenue)//2:].mean()
    trend_change = ((second_half_mean - first_half_mean) / first_half_mean) * 100
    
    print(f"First half mean: ${first_half_mean:.2f}")
    print(f"Second half mean: ${second_half_mean:.2f}")
    print(f"Trend change: {trend_change:.2f}%")
    
    if abs(trend_change) > 10:
        print("⚠️  Series shows significant trend - may need differencing")
    else:
        print("✅ Series appears relatively stationary")
    
    # Variance stability
    first_half_std = revenue[:len(revenue)//2].std()
    second_half_std = revenue[len(revenue)//2:].std()
    variance_change = ((second_half_std - first_half_std) / first_half_std) * 100
    
    print(f"Variance change: {variance_change:.2f}%")
    
    if abs(variance_change) > 20:
        print("⚠️  Series shows changing variance - may need transformation")
    else:
        print("✅ Variance appears stable")
    
    return {
        'trend_change_pct': trend_change,
        'variance_change_pct': variance_change,
        'needs_differencing': abs(trend_change) > 10,
        'needs_transformation': abs(variance_change) > 20
    }

def analyze_business_patterns(df):
    """Analyze business patterns in the data"""
    print("\n" + "="*50)
    print("BUSINESS PATTERN ANALYSIS")
    print("="*50)
    
    # Customer analysis
    customer_stats = df.groupby('UserId').agg({
        'TotalValue': ['sum', 'count', 'mean'],
        'TransactionTime': ['min', 'max']
    }).round(2)
    
    customer_stats.columns = ['TotalSpent', 'TransactionCount', 'AvgTransactionValue', 'FirstTransaction', 'LastTransaction']
    customer_stats = customer_stats.reset_index()
    
    print(f"Customer Analysis:")
    print(f"Average customer lifetime value: ${customer_stats['TotalSpent'].mean():.2f}")
    print(f"Average transactions per customer: {customer_stats['TransactionCount'].mean():.1f}")
    print(f"Top 10% customers contribute: {(customer_stats.nlargest(int(len(customer_stats)*0.1), 'TotalSpent')['TotalSpent'].sum() / customer_stats['TotalSpent'].sum() * 100):.1f}% of revenue")
    
    # Product analysis
    product_stats = df.groupby('ItemCode').agg({
        'TotalValue': 'sum',
        'NumberOfItemsPurchased': 'sum',
        'TransactionId': 'count'
    }).round(2)
    
    product_stats.columns = ['TotalRevenue', 'TotalQuantity', 'TransactionCount']
    product_stats = product_stats.reset_index()
    
    print(f"\nProduct Analysis:")
    print(f"Top 20% products contribute: {(product_stats.nlargest(int(len(product_stats)*0.2), 'TotalRevenue')['TotalRevenue'].sum() / product_stats['TotalRevenue'].sum() * 100):.1f}% of revenue")
    
    # Geographic analysis
    country_stats = df.groupby('Country').agg({
        'TotalValue': 'sum',
        'UserId': 'nunique'
    }).round(2)
    
    country_stats.columns = ['TotalRevenue', 'UniqueCustomers']
    country_stats = country_stats.sort_values('TotalRevenue', ascending=False)
    
    print(f"\nGeographic Analysis:")
    print(f"Top 5 countries by revenue:")
    for i, (country, row) in enumerate(country_stats.head().iterrows()):
        print(f"  {i+1}. {country}: ${row['TotalRevenue']:,.2f} ({row['UniqueCustomers']} customers)")
    
    return customer_stats, product_stats, country_stats

def calculate_data_quality_score(df, daily_ts, stationarity_info):
    """Calculate comprehensive data quality score"""
    print("\n" + "="*50)
    print("DATA QUALITY SCORING")
    print("="*50)
    
    scores = {}
    
    # 1. Completeness Score (25 points)
    completeness = (1 - df.isnull().sum().sum() / (df.shape[0] * df.shape[1])) * 100
    completeness_score = min(25, completeness * 0.25)
    scores['completeness'] = completeness_score
    print(f"Completeness Score: {completeness_score:.1f}/25 ({completeness:.1f}% complete)")
    
    # 2. Consistency Score (20 points)
    # Check data type consistency, reasonable value ranges
    consistency_issues = 0
    
    # Check for reasonable price ranges (0.01 to 10000)
    price_issues = ((df['CostPerItem'] < 0.01) | (df['CostPerItem'] > 10000)).sum()
    consistency_issues += price_issues
    
    # Check for reasonable quantities (1 to 1000)
    qty_issues = ((df['NumberOfItemsPurchased'] < 1) | (df['NumberOfItemsPurchased'] > 1000)).sum()
    consistency_issues += qty_issues
    
    consistency_rate = 1 - (consistency_issues / len(df))
    consistency_score = consistency_rate * 20
    scores['consistency'] = consistency_score
    print(f"Consistency Score: {consistency_score:.1f}/20 ({consistency_rate*100:.1f}% consistent)")
    
    # 3. Time Series Quality Score (25 points)
    ts_score = 0
    
    # Length adequacy (need at least 100 points for good ARIMA)
    if len(daily_ts) >= 200:
        ts_score += 10
    elif len(daily_ts) >= 100:
        ts_score += 7
    else:
        ts_score += 3
    
    # Frequency consistency (daily data)
    ts_score += 5  # We have daily data
    
    # Stationarity readiness
    if not stationarity_info['needs_differencing'] and not stationarity_info['needs_transformation']:
        ts_score += 10
    elif not stationarity_info['needs_differencing'] or not stationarity_info['needs_transformation']:
        ts_score += 7
    else:
        ts_score += 3
    
    scores['time_series'] = ts_score
    print(f"Time Series Quality Score: {ts_score:.1f}/25")
    
    # 4. Business Relevance Score (15 points)
    # Revenue variability (good for forecasting)
    revenue_cv = daily_ts['DailyRevenue'].std() / daily_ts['DailyRevenue'].mean()
    if 0.1 <= revenue_cv <= 0.5:  # Good variability for modeling
        business_score = 15
    elif 0.05 <= revenue_cv <= 0.7:
        business_score = 12
    else:
        business_score = 8
    
    scores['business_relevance'] = business_score
    print(f"Business Relevance Score: {business_score:.1f}/15 (CV: {revenue_cv:.3f})")
    
    # 5. Data Richness Score (15 points)
    # Multiple dimensions for analysis
    richness_score = 0
    richness_score += min(5, df['UserId'].nunique() / 1000)  # Customer diversity
    richness_score += min(5, df['ItemCode'].nunique() / 500)  # Product diversity
    richness_score += min(5, df['Country'].nunique() / 10)  # Geographic diversity
    
    scores['richness'] = richness_score
    print(f"Data Richness Score: {richness_score:.1f}/15")
    
    # Total Score
    total_score = sum(scores.values())
    scores['total'] = total_score
    
    print(f"\n🎯 TOTAL DATA QUALITY SCORE: {total_score:.1f}/100")
    
    # Grade assignment
    if total_score >= 90:
        grade = "A+ (Excellent)"
    elif total_score >= 80:
        grade = "A (Very Good)"
    elif total_score >= 70:
        grade = "B (Good)"
    elif total_score >= 60:
        grade = "C (Fair)"
    else:
        grade = "D (Needs Improvement)"
    
    print(f"📊 DATA QUALITY GRADE: {grade}")
    
    return scores, grade

def generate_visualizations(daily_ts):
    """Generate key visualizations for the dataset"""
    print("\nGenerating visualizations...")
    
    # Create figure with subplots
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Time Series Analysis Dashboard', fontsize=16, fontweight='bold')
    
    # 1. Daily Revenue Time Series
    axes[0, 0].plot(daily_ts['Date'], daily_ts['DailyRevenue'], linewidth=1, alpha=0.7)
    axes[0, 0].plot(daily_ts['Date'], daily_ts['MA_30'], linewidth=2, color='red', label='30-day MA')
    axes[0, 0].set_title('Daily Revenue Time Series')
    axes[0, 0].set_ylabel('Revenue ($)')
    axes[0, 0].legend()
    axes[0, 0].tick_params(axis='x', rotation=45)
    
    # 2. Revenue Distribution
    axes[0, 1].hist(daily_ts['DailyRevenue'], bins=30, alpha=0.7, edgecolor='black')
    axes[0, 1].set_title('Daily Revenue Distribution')
    axes[0, 1].set_xlabel('Revenue ($)')
    axes[0, 1].set_ylabel('Frequency')
    
    # 3. Monthly Seasonality
    monthly_avg = daily_ts.groupby('Month')['DailyRevenue'].mean()
    axes[1, 0].bar(monthly_avg.index, monthly_avg.values, alpha=0.7)
    axes[1, 0].set_title('Monthly Seasonality')
    axes[1, 0].set_xlabel('Month')
    axes[1, 0].set_ylabel('Average Revenue ($)')
    
    # 4. Weekly Seasonality
    weekly_avg = daily_ts.groupby('DayOfWeek')['DailyRevenue'].mean()
    days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
    # Ensure we have data for all 7 days
    weekly_data = [weekly_avg.get(i, 0) for i in range(7)]
    axes[1, 1].bar(range(7), weekly_data, alpha=0.7)
    axes[1, 1].set_title('Weekly Seasonality')
    axes[1, 1].set_xlabel('Day of Week')
    axes[1, 1].set_ylabel('Average Revenue ($)')
    axes[1, 1].set_xticks(range(7))
    axes[1, 1].set_xticklabels(days)
    
    plt.tight_layout()
    plt.savefig('data_analysis_dashboard.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ Visualizations saved to: data_analysis_dashboard.png")

def main():
    """Main evaluation pipeline"""
    print("Starting data evaluation and analysis...")
    
    # Load data
    df, daily_ts = load_cleaned_data()
    
    # Perform analyses
    daily_ts = analyze_time_series_properties(daily_ts)
    stationarity_info = check_stationarity(daily_ts)
    customer_stats, product_stats, country_stats = analyze_business_patterns(df)
    scores, grade = calculate_data_quality_score(df, daily_ts, stationarity_info)
    
    # Generate visualizations
    generate_visualizations(daily_ts)
    
    # Save analysis results
    analysis_results = {
        'timestamp': datetime.now().isoformat(),
        'data_quality_scores': scores,
        'data_quality_grade': grade,
        'stationarity_analysis': stationarity_info,
        'time_series_length': len(daily_ts),
        'date_range': {
            'start': daily_ts['Date'].min().isoformat(),
            'end': daily_ts['Date'].max().isoformat()
        },
        'business_metrics': {
            'total_customers': df['UserId'].nunique(),
            'total_products': df['ItemCode'].nunique(),
            'total_countries': df['Country'].nunique(),
            'total_revenue': df['TotalValue'].sum(),
            'avg_daily_revenue': daily_ts['DailyRevenue'].mean()
        }
    }
    
    # Save detailed results
    customer_stats.to_csv('customer_analysis.csv', index=False)
    product_stats.to_csv('product_analysis.csv', index=False)
    country_stats.to_csv('country_analysis.csv')
    
    import json
    with open('data_evaluation_results.json', 'w') as f:
        json.dump(analysis_results, f, indent=2, default=str)
    
    print("\n✅ Data evaluation completed successfully!")
    print("📁 Results saved to:")
    print("   - customer_analysis.csv")
    print("   - product_analysis.csv") 
    print("   - country_analysis.csv")
    print("   - data_evaluation_results.json")
    print("   - data_analysis_dashboard.png")
    
    return analysis_results

if __name__ == "__main__":
    results = main()
