#!/usr/bin/env python3
"""
Minimal Web Application for Time Series Forecasting
"""

from flask import Flask, render_template, request, jsonify
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

app = Flask(__name__)
app.secret_key = 'minimal_app_key'

# Mock data for testing
MOCK_STATS = {
    'model_name': 'ARIMA[6, 0, 6]',
    'model_type': 'Grid Search ARIMA',
    'mape': '123.40%',
    'mae': '0.3056',
    'rmse': '0.4036',
    'aic': '150.25',
    'diagnostic_score': '60.0%',
    'data_points': 304,
    'date_range': '2018-02-12 to 2018-12-09'
}

MOCK_MODEL = {
    'basic_info': {
        'Model': 'ARIMA[6, 0, 6]',
        'Type': 'Grid Search ARIMA',
        'Order': '(6, 0, 6)',
        'AIC': '150.25',
        'BIC': '175.30',
        'Log Likelihood': '-69.12'
    },
    'performance': {
        'MAPE': '123.40%',
        'MAE': '0.3056',
        'RMSE': '0.4036',
        'Target MAPE': '<5%',
        'Target RMSE': '<10%'
    },
    'diagnostics': {
        'ljung_box_test': {
            'value': 0.0234,
            'status': 'PASS',
            'description': 'No autocorrelation in residuals'
        },
        'shapiro_wilk_test': {
            'value': 0.8765,
            'status': 'FAIL',
            'description': 'Residuals not normally distributed'
        }
    },
    'diagnostic_score': 60.0,
    'recommendation': 'Model is usable but requires monitoring due to high MAPE'
}

@app.route('/')
def home():
    """Dashboard page"""
    return render_template('dashboard.html', stats=MOCK_STATS)

@app.route('/forecast')
def forecast_page():
    """Forecasting page"""
    return render_template('forecast.html')

@app.route('/model-info')
def model_info_page():
    """Model information page"""
    return render_template('model_info.html', model=MOCK_MODEL)

@app.route('/api/forecast', methods=['POST'])
def api_forecast():
    """Generate mock forecast"""
    try:
        data = request.get_json()
        steps = int(data.get('steps', 30))
        
        # Generate mock forecast data
        base_value = 100
        forecasts = []
        
        for i in range(steps):
            # Simple trend with some randomness
            value = base_value + (i * 2) + np.random.normal(0, 10)
            date = (datetime.now() + timedelta(days=i+1)).strftime('%Y-%m-%d')
            
            forecast_item = {
                'date': date,
                'value': float(value)
            }
            
            # Add confidence intervals if requested
            if data.get('confidence_intervals', True):
                confidence_level = float(data.get('confidence_level', 0.95))
                margin = value * 0.2  # 20% margin for demo
                forecast_item['confidence_interval'] = {
                    'lower': float(value - margin),
                    'upper': float(value + margin),
                    'level': confidence_level
                }
            
            forecasts.append(forecast_item)
        
        # Calculate summary
        values = [f['value'] for f in forecasts]
        summary = {
            'steps': steps,
            'mean': float(np.mean(values)),
            'min': float(np.min(values)),
            'max': float(np.max(values)),
            'total': float(np.sum(values)),
            'trend': 'increasing' if values[-1] > values[0] else 'decreasing'
        }
        
        return jsonify({
            'forecasts': forecasts,
            'summary': summary
        })
        
    except Exception as e:
        return jsonify({'error': f'Forecast generation failed: {str(e)}'}), 500

@app.route('/api/historical-data')
def api_historical_data():
    """Return mock historical data"""
    try:
        # Generate mock historical data
        data = []
        base_date = datetime.now() - timedelta(days=100)
        
        for i in range(100):
            date = (base_date + timedelta(days=i)).strftime('%Y-%m-%d')
            revenue = 80 + (i * 0.5) + np.random.normal(0, 15)
            data.append({
                'date': date,
                'revenue': max(0, float(revenue))  # Ensure non-negative
            })
        
        return jsonify({
            'data': data,
            'summary': {
                'total_points': len(data),
                'date_range': {
                    'start': data[0]['date'],
                    'end': data[-1]['date']
                }
            }
        })
        
    except Exception as e:
        return jsonify({'error': f'Failed to load historical data: {str(e)}'}), 500

@app.route('/api/model-summary')
def api_model_summary():
    """Return mock model summary"""
    return jsonify({
        'model_info': {
            'Model': 'ARIMA[6, 0, 6]',
            'Type': 'Grid Search ARIMA',
            'MAPE': 123.40,
            'MAE': 0.3056,
            'RMSE': 0.4036
        },
        'diagnostics': {
            'score': 60.0,
            'recommendation': 'Model is usable but requires monitoring',
            'tests': {
                'ljung_box_test': {'status': 'PASS'},
                'shapiro_wilk_test': {'status': 'FAIL'}
            }
        },
        'parameters': {
            'aic': 150.25,
            'bic': 175.30,
            'order': [6, 0, 6]
        }
    })

@app.errorhandler(404)
def not_found(error):
    return render_template('error.html', error="Page not found"), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('error.html', error="Internal server error"), 500

if __name__ == '__main__':
    print("Starting Minimal Time Series Forecasting Web Application...")
    print("="*60)
    print("✅ Using mock data for demonstration")
    print("\nWeb Application Features:")
    print("- Dashboard with model overview")
    print("- Interactive forecasting interface")
    print("- Model information and diagnostics")
    print("- Mock data visualization")
    print("- REST API endpoints")
    print("="*60)
    
    app.run(host='0.0.0.0', port=5004, debug=True)
