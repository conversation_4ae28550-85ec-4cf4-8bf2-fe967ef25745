#!/usr/bin/env python3
"""
Data Cleaning Script for Transaction Dataset
This script cleans the transaction_data.csv file using pandas for time series analysis.
"""

import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_data(file_path):
    """Load the raw transaction data"""
    print("Loading raw data...")
    df = pd.read_csv(file_path)
    print(f"Original dataset shape: {df.shape}")
    return df

def clean_transaction_time(df):
    """Convert TransactionTime to proper datetime format"""
    print("Cleaning TransactionTime column...")
    
    # Convert to datetime
    df['TransactionTime'] = pd.to_datetime(df['TransactionTime'], format='%a %b %d %H:%M:%S IST %Y')
    
    # Extract additional time features for analysis
    df['Year'] = df['TransactionTime'].dt.year
    df['Month'] = df['TransactionTime'].dt.month
    df['Day'] = df['TransactionTime'].dt.day
    df['Hour'] = df['TransactionTime'].dt.hour
    df['DayOfWeek'] = df['TransactionTime'].dt.dayofweek
    df['Date'] = df['TransactionTime'].dt.date
    
    print(f"Date range: {df['TransactionTime'].min()} to {df['TransactionTime'].max()}")
    return df

def handle_missing_values(df):
    """Handle missing values in the dataset"""
    print("Handling missing values...")
    
    # Check missing values
    missing_before = df.isnull().sum()
    print("Missing values before cleaning:")
    print(missing_before[missing_before > 0])
    
    # Fill missing ItemDescription with 'Unknown Item'
    df['ItemDescription'] = df['ItemDescription'].fillna('Unknown Item')
    
    # Verify no missing values remain
    missing_after = df.isnull().sum()
    print("Missing values after cleaning:")
    print(missing_after[missing_after > 0])
    
    return df

def handle_negative_values(df):
    """Handle negative and invalid values"""
    print("Handling negative and invalid values...")
    
    # Count negative values before cleaning
    print("Negative values before cleaning:")
    print(f"Negative UserIds: {(df['UserId'] < 0).sum()}")
    print(f"Negative NumberOfItemsPurchased: {(df['NumberOfItemsPurchased'] < 0).sum()}")
    print(f"Negative CostPerItem: {(df['CostPerItem'] < 0).sum()}")
    
    # Remove rows with negative UserIds (likely invalid records)
    df = df[df['UserId'] >= 0].copy()
    
    # Handle negative quantities - these might be returns, convert to positive
    df['NumberOfItemsPurchased'] = df['NumberOfItemsPurchased'].abs()
    
    # Handle negative costs - remove these few records as they're likely errors
    df = df[df['CostPerItem'] >= 0].copy()
    
    # Remove zero quantities and zero costs as they don't contribute to revenue analysis
    df = df[(df['NumberOfItemsPurchased'] > 0) & (df['CostPerItem'] > 0)].copy()
    
    print(f"Dataset shape after removing negative values: {df.shape}")
    return df

def remove_duplicates(df):
    """Remove duplicate transactions"""
    print("Removing duplicates...")
    
    initial_count = len(df)
    
    # Remove exact duplicates
    df = df.drop_duplicates().copy()
    
    # Remove duplicates based on key business logic
    # (same user, same item, same time, same quantity, same cost)
    df = df.drop_duplicates(subset=['UserId', 'ItemCode', 'TransactionTime', 
                                   'NumberOfItemsPurchased', 'CostPerItem']).copy()
    
    final_count = len(df)
    print(f"Removed {initial_count - final_count} duplicate records")
    print(f"Dataset shape after removing duplicates: {df.shape}")
    
    return df

def handle_outliers(df):
    """Handle extreme outliers in quantities and prices"""
    print("Handling outliers...")
    
    # Calculate total transaction value
    df['TotalValue'] = df['NumberOfItemsPurchased'] * df['CostPerItem']
    
    # Define outlier thresholds using IQR method
    def get_outlier_bounds(series, multiplier=3):
        Q1 = series.quantile(0.25)
        Q3 = series.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - multiplier * IQR
        upper_bound = Q3 + multiplier * IQR
        return lower_bound, upper_bound
    
    # Handle quantity outliers
    qty_lower, qty_upper = get_outlier_bounds(df['NumberOfItemsPurchased'])
    qty_outliers_before = ((df['NumberOfItemsPurchased'] < qty_lower) | 
                          (df['NumberOfItemsPurchased'] > qty_upper)).sum()
    
    # Handle price outliers
    price_lower, price_upper = get_outlier_bounds(df['CostPerItem'])
    price_outliers_before = ((df['CostPerItem'] < price_lower) | 
                            (df['CostPerItem'] > price_upper)).sum()
    
    # Remove extreme outliers (keep reasonable business transactions)
    df = df[(df['NumberOfItemsPurchased'] <= qty_upper) & 
            (df['CostPerItem'] <= price_upper)].copy()
    
    print(f"Removed {qty_outliers_before} quantity outliers")
    print(f"Removed {price_outliers_before} price outliers")
    print(f"Dataset shape after outlier removal: {df.shape}")
    
    return df

def create_time_series_features(df):
    """Create features suitable for time series analysis"""
    print("Creating time series features...")
    
    # Sort by transaction time
    df = df.sort_values('TransactionTime').copy()
    
    # Create daily aggregated revenue data for time series modeling
    daily_revenue = df.groupby('Date').agg({
        'TotalValue': 'sum',
        'NumberOfItemsPurchased': 'sum',
        'TransactionId': 'count'
    }).reset_index()
    
    daily_revenue.columns = ['Date', 'DailyRevenue', 'DailyQuantity', 'DailyTransactions']
    daily_revenue['Date'] = pd.to_datetime(daily_revenue['Date'])
    daily_revenue = daily_revenue.sort_values('Date')
    
    print(f"Created daily time series with {len(daily_revenue)} data points")
    print(f"Date range: {daily_revenue['Date'].min()} to {daily_revenue['Date'].max()}")
    
    return df, daily_revenue

def generate_data_quality_report(df_original, df_cleaned, daily_revenue):
    """Generate a comprehensive data quality report"""
    print("\n" + "="*50)
    print("DATA QUALITY REPORT")
    print("="*50)
    
    print(f"Original dataset size: {df_original.shape[0]:,} rows")
    print(f"Cleaned dataset size: {df_cleaned.shape[0]:,} rows")
    print(f"Data retention rate: {(df_cleaned.shape[0]/df_original.shape[0]*100):.2f}%")
    
    print(f"\nTime series data points: {len(daily_revenue)}")
    print(f"Date range: {daily_revenue['Date'].min().strftime('%Y-%m-%d')} to {daily_revenue['Date'].max().strftime('%Y-%m-%d')}")
    
    print(f"\nDaily revenue statistics:")
    print(f"Mean daily revenue: ${daily_revenue['DailyRevenue'].mean():.2f}")
    print(f"Median daily revenue: ${daily_revenue['DailyRevenue'].median():.2f}")
    print(f"Max daily revenue: ${daily_revenue['DailyRevenue'].max():.2f}")
    print(f"Min daily revenue: ${daily_revenue['DailyRevenue'].min():.2f}")
    
    print(f"\nData quality metrics:")
    print(f"Missing values: {df_cleaned.isnull().sum().sum()}")
    print(f"Duplicate records: 0 (removed)")
    print(f"Negative values: 0 (cleaned)")
    print(f"Unique customers: {df_cleaned['UserId'].nunique():,}")
    print(f"Unique products: {df_cleaned['ItemCode'].nunique():,}")
    print(f"Countries: {df_cleaned['Country'].nunique()}")
    
    return {
        'original_size': df_original.shape[0],
        'cleaned_size': df_cleaned.shape[0],
        'retention_rate': df_cleaned.shape[0]/df_original.shape[0]*100,
        'time_series_points': len(daily_revenue),
        'date_range_days': (daily_revenue['Date'].max() - daily_revenue['Date'].min()).days,
        'mean_daily_revenue': daily_revenue['DailyRevenue'].mean(),
        'unique_customers': df_cleaned['UserId'].nunique(),
        'unique_products': df_cleaned['ItemCode'].nunique()
    }

def main():
    """Main data cleaning pipeline"""
    print("Starting data cleaning pipeline...")
    
    # Load data
    df_original = load_data('transaction_data.csv')
    df = df_original.copy()
    
    # Apply cleaning steps
    df = clean_transaction_time(df)
    df = handle_missing_values(df)
    df = handle_negative_values(df)
    df = remove_duplicates(df)
    df = handle_outliers(df)
    df, daily_revenue = create_time_series_features(df)
    
    # Generate report
    quality_metrics = generate_data_quality_report(df_original, df, daily_revenue)
    
    # Save cleaned data
    print("\nSaving cleaned datasets...")
    df.to_csv('cleaned_transaction_data.csv', index=False)
    daily_revenue.to_csv('daily_revenue_timeseries.csv', index=False)
    
    print("Data cleaning completed successfully!")
    print(f"Cleaned transaction data saved to: cleaned_transaction_data.csv")
    print(f"Time series data saved to: daily_revenue_timeseries.csv")
    
    return df, daily_revenue, quality_metrics

if __name__ == "__main__":
    cleaned_df, daily_ts, metrics = main()
