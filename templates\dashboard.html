{% extends "base.html" %}

{% block title %}Dashboard - Time Series Forecasting{% endblock %}

{% block content %}
<div class="container">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="display-6 mb-0">
                        <i class="fas fa-tachometer-alt text-primary me-2"></i>
                        Forecasting Dashboard
                    </h1>
                    <p class="text-muted">Overview of your time series forecasting system</p>
                </div>
                <div>
                    <a href="{{ url_for('forecast_page') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-crystal-ball me-2"></i>Create Forecast
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-robot fa-2x"></i>
                    </div>
                    <h5 class="card-title">Model</h5>
                    <h3 class="text-primary mb-0">{{ stats.model_name }}</h3>
                    <small class="text-muted">{{ stats.model_type }}</small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-percentage fa-2x"></i>
                    </div>
                    <h5 class="card-title">MAPE</h5>
                    <h3 class="text-warning mb-0">{{ stats.mape }}</h3>
                    <small class="text-muted">Mean Absolute Percentage Error</small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-info mb-2">
                        <i class="fas fa-chart-bar fa-2x"></i>
                    </div>
                    <h5 class="card-title">AIC Score</h5>
                    <h3 class="text-info mb-0">{{ stats.aic }}</h3>
                    <small class="text-muted">Akaike Information Criterion</small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                    <h5 class="card-title">Diagnostics</h5>
                    <h3 class="text-success mb-0">{{ stats.diagnostic_score }}</h3>
                    <small class="text-muted">Model Quality Score</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Model Performance and Data Overview -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line text-primary me-2"></i>
                        Historical Data Overview
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="historicalChart" height="300"></canvas>
                    <div class="mt-3">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="border-end">
                                    <h6 class="text-muted mb-1">Data Points</h6>
                                    <h4 class="text-primary mb-0">{{ stats.data_points }}</h4>
                                </div>
                            </div>
                            <div class="col-8">
                                <h6 class="text-muted mb-1">Date Range</h6>
                                <h6 class="text-dark mb-0">{{ stats.date_range }}</h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs text-primary me-2"></i>
                        Model Performance
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span class="text-muted">MAPE</span>
                            <span class="fw-bold">{{ stats.mape }}</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-warning" style="width: 100%"></div>
                        </div>
                        <small class="text-muted">Target: &lt;5%</small>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span class="text-muted">MAE</span>
                            <span class="fw-bold">{{ stats.mae }}</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-info" style="width: 75%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <span class="text-muted">RMSE</span>
                            <span class="fw-bold">{{ stats.rmse }}</span>
                        </div>
                        <div class="progress" style="height: 8px;">
                            <div class="progress-bar bg-danger" style="width: 85%"></div>
                        </div>
                        <small class="text-muted">Target: &lt;10%</small>
                    </div>
                    
                    <hr>
                    
                    <div class="text-center">
                        <div class="mb-2">
                            <span class="badge bg-success fs-6">{{ stats.diagnostic_score }}</span>
                        </div>
                        <p class="text-muted mb-0">Overall Model Quality</p>
                        <a href="{{ url_for('model_info_page') }}" class="btn btn-outline-primary btn-sm mt-2">
                            View Details
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt text-primary me-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <a href="{{ url_for('forecast_page') }}" class="btn btn-primary btn-lg">
                                    <i class="fas fa-crystal-ball me-2"></i>
                                    Generate Forecast
                                </a>
                            </div>
                            <small class="text-muted d-block mt-1 text-center">
                                Create new time series forecasts
                            </small>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <a href="{{ url_for('model_info_page') }}" class="btn btn-outline-primary btn-lg">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Model Details
                                </a>
                            </div>
                            <small class="text-muted d-block mt-1 text-center">
                                View model performance and diagnostics
                            </small>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <button class="btn btn-outline-secondary btn-lg" onclick="downloadSampleData()">
                                    <i class="fas fa-download me-2"></i>
                                    Export Data
                                </button>
                            </div>
                            <small class="text-muted d-block mt-1 text-center">
                                Download historical data and forecasts
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Load and display historical data chart
document.addEventListener('DOMContentLoaded', function() {
    loadHistoricalChart();
});

async function loadHistoricalChart() {
    try {
        const response = await fetch('/api/historical-data');
        const data = await response.json();
        
        if (data.error) {
            console.error('Error loading historical data:', data.error);
            return;
        }
        
        const ctx = document.getElementById('historicalChart').getContext('2d');
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.data.map(item => item.date),
                datasets: [{
                    label: 'Daily Revenue',
                    data: data.data.map(item => item.revenue),
                    borderColor: '#0d6efd',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            unit: 'month'
                        },
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Revenue ($)'
                        },
                        beginAtZero: true
                    }
                }
            }
        });
        
    } catch (error) {
        console.error('Error loading historical chart:', error);
    }
}

function downloadSampleData() {
    // Placeholder for data export functionality
    alert('Data export functionality will be implemented in the next phase.');
}
</script>
{% endblock %}
