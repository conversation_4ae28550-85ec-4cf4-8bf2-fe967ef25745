/* Custom CSS for Time Series Forecasting Web Application */

/* Global Styles */
:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 0.5rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    line-height: 1.6;
}

/* Navigation Styles */
.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
}

/* Card Styles */
.card {
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-lg);
}

.card-header {
    border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
    font-weight: 600;
}

/* Button Styles */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

/* Progress Bar Styles */
.progress {
    border-radius: var(--border-radius);
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: var(--border-radius);
    transition: width 0.6s ease;
}

/* Badge Styles */
.badge {
    border-radius: var(--border-radius);
    font-weight: 500;
}

/* Chart Container */
.chart-container {
    position: relative;
    height: 400px;
    margin: 1rem 0;
}

/* Form Styles */
.form-control, .form-select {
    border-radius: var(--border-radius);
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

/* Alert Styles */
.alert {
    border-radius: var(--border-radius);
    border: none;
    font-weight: 500;
}

.alert-info {
    background-color: rgba(13, 202, 240, 0.1);
    color: #055160;
}

.alert-success {
    background-color: rgba(25, 135, 84, 0.1);
    color: #0a3622;
}

.alert-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #664d03;
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #58151c;
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Forecast Results */
.forecast-result {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    margin: 1rem 0;
}

.forecast-value {
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Model Info Cards */
.model-info-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border: none;
}

.model-info-card .card-title {
    color: white;
    opacity: 0.9;
}

/* Diagnostic Status */
.diagnostic-pass {
    color: var(--success-color);
    font-weight: 600;
}

.diagnostic-fail {
    color: var(--danger-color);
    font-weight: 600;
}

.diagnostic-warning {
    color: var(--warning-color);
    font-weight: 600;
}

/* Footer */
footer {
    margin-top: auto;
    border-top: 1px solid #dee2e6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .display-6 {
        font-size: 2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-lg {
        padding: 0.5rem 1rem;
        font-size: 1rem;
    }
    
    .forecast-value {
        font-size: 2rem;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .display-6 {
        font-size: 1.75rem;
    }
    
    .forecast-value {
        font-size: 1.75rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: var(--border-radius);
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.shadow-custom {
    box-shadow: var(--box-shadow-lg);
}

.border-custom {
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: var(--border-radius);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
