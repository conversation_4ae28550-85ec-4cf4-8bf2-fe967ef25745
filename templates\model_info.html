{% extends "base.html" %}

{% block title %}Model Information - Time Series Forecasting{% endblock %}

{% block content %}
<div class="container">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="display-6 mb-0">
                        <i class="fas fa-cogs text-primary me-2"></i>
                        Model Information
                    </h1>
                    <p class="text-muted">Detailed information about your ARIMA forecasting model</p>
                </div>
                <div>
                    <a href="{{ url_for('home') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Model Overview -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-robot me-2"></i>
                        Model Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">Basic Information</h6>
                            <table class="table table-borderless">
                                {% for key, value in model.basic_info.items() %}
                                <tr>
                                    <td class="fw-bold">{{ key }}:</td>
                                    <td>{{ value }}</td>
                                </tr>
                                {% endfor %}
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted mb-3">Performance Metrics</h6>
                            <table class="table table-borderless">
                                {% for key, value in model.performance.items() %}
                                <tr>
                                    <td class="fw-bold">{{ key }}:</td>
                                    <td>
                                        {% if 'Target' in key %}
                                            <span class="text-muted">{{ value }}</span>
                                        {% elif 'MAPE' in key %}
                                            <span class="badge bg-warning">{{ value }}</span>
                                        {% else %}
                                            {{ value }}
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Diagnostic Results -->
    <div class="row mb-4">
        <div class="col-lg-8 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-stethoscope me-2"></i>
                        Diagnostic Tests
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Test</th>
                                    <th>Result</th>
                                    <th>Status</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for test_name, test_result in model.diagnostics.items() %}
                                <tr>
                                    <td class="fw-bold">{{ test_name.replace('_', ' ').title() }}</td>
                                    <td>
                                        {% if test_result.value is number %}
                                            {{ "%.4f"|format(test_result.value) }}
                                        {% else %}
                                            {{ test_result.value }}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if test_result.status == 'PASS' %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-check me-1"></i>PASS
                                            </span>
                                        {% elif test_result.status == 'FAIL' %}
                                            <span class="badge bg-danger">
                                                <i class="fas fa-times me-1"></i>FAIL
                                            </span>
                                        {% else %}
                                            <span class="badge bg-warning">
                                                <i class="fas fa-exclamation me-1"></i>WARNING
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="text-muted small">{{ test_result.description }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Overall Score
                    </h5>
                </div>
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div class="display-4 text-success fw-bold">
                            {{ "%.1f"|format(model.diagnostic_score) }}%
                        </div>
                        <p class="text-muted">Diagnostic Score</p>
                    </div>
                    
                    <div class="progress mb-3" style="height: 20px;">
                        <div class="progress-bar bg-success" 
                             style="width: {{ model.diagnostic_score }}%"
                             role="progressbar">
                        </div>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-lightbulb me-2"></i>
                        <strong>Recommendation:</strong><br>
                        {{ model.recommendation }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Model Interpretation -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-book me-2"></i>
                        Model Interpretation Guide
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">Performance Metrics</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <strong>MAPE (Mean Absolute Percentage Error):</strong>
                                    <br><small class="text-muted">Measures forecast accuracy as a percentage. Lower is better. Target: &lt;5%</small>
                                </li>
                                <li class="mb-2">
                                    <strong>MAE (Mean Absolute Error):</strong>
                                    <br><small class="text-muted">Average absolute difference between actual and predicted values.</small>
                                </li>
                                <li class="mb-2">
                                    <strong>RMSE (Root Mean Square Error):</strong>
                                    <br><small class="text-muted">Penalizes larger errors more heavily. Target: &lt;10%</small>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">Model Selection Criteria</h6>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <strong>AIC (Akaike Information Criterion):</strong>
                                    <br><small class="text-muted">Balances model fit and complexity. Lower values indicate better models.</small>
                                </li>
                                <li class="mb-2">
                                    <strong>BIC (Bayesian Information Criterion):</strong>
                                    <br><small class="text-muted">Similar to AIC but penalizes complexity more heavily.</small>
                                </li>
                                <li class="mb-2">
                                    <strong>Log Likelihood:</strong>
                                    <br><small class="text-muted">Measures how well the model fits the data. Higher is better.</small>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">Diagnostic Tests Explained</h6>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="card bg-light border-0 h-100">
                                        <div class="card-body">
                                            <h6 class="card-title">Ljung-Box Test</h6>
                                            <p class="card-text small">Tests for autocorrelation in residuals. PASS means residuals are independent (good).</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card bg-light border-0 h-100">
                                        <div class="card-body">
                                            <h6 class="card-title">Shapiro-Wilk Test</h6>
                                            <p class="card-text small">Tests for normality of residuals. PASS means residuals are normally distributed (good).</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card bg-light border-0 h-100">
                                        <div class="card-body">
                                            <h6 class="card-title">Confidence Interval Coverage</h6>
                                            <p class="card-text small">Percentage of actual values within predicted confidence intervals. Should be close to confidence level.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
