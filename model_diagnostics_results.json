{"model_info": {"Model": "ARIMA[6, 0, 6]", "Type": "Grid Search ARIMA", "MAE": 0.3056186890336773, "RMSE": 0.4036197636261589, "MAPE": 123.39657306278625, "MAE_%": 6599.93223484115, "RMSE_%": 8716.29643134066, "Meets_MAPE_Target": false, "Meets_RMSE_Target": false}, "residual_analysis": {"mean": -0.010935950238405706, "std": 0.3397259489710728, "skewness": -0.9668597603875624, "kurtosis": 1.4889656575363004, "shapiro_stat": 0.9451968204873099, "shapiro_p": 6.216483784903169e-08, "normal_residuals": "False", "autocorrelation_lags": [], "no_autocorrelation": true}, "autocorrelation_analysis": {"acf_significant_lags": 0, "pacf_significant_lags": 0, "good_white_noise": true}, "assumptions_validation": {"stationarity": true, "linearity": true, "normal_residuals": "False", "no_autocorrelation": true, "homoscedasticity": "True", "white_noise_residuals": true}, "confidence_intervals_analysis": {"coverage_rate": 98.33333333333333, "good_coverage": "False", "avg_width": 1.7425270550968754, "width_cv": 0.048614764276150864, "consistent_width": "True"}, "diagnostic_summary": {"diagnostic_score": 60.0, "recommendation": "Model can be used with monitoring", "diagnostic_tests": {"Residual Normality": "False", "No Autocorrelation": true, "White Noise Residuals": true, "Good CI Coverage": "False", "Consistent CI Width": "True"}}, "model_parameters": {"aic": 193.57808180342295, "bic": 242.53843695752778, "log_likelihood": -82.78904090171147}}