#!/usr/bin/env python3
"""
ARIMA Data Preprocessing Script
This script prepares the time series data for ARIMA modeling by addressing stationarity issues.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
from statsmodels.tsa.stattools import adfuller, kpss
from statsmodels.stats.diagnostic import acorr_ljungbox
import warnings
warnings.filterwarnings('ignore')

def load_time_series_data():
    """Load the daily revenue time series data"""
    print("Loading time series data...")
    
    daily_ts = pd.read_csv('daily_revenue_timeseries.csv')
    daily_ts['Date'] = pd.to_datetime(daily_ts['Date'])
    daily_ts = daily_ts.sort_values('Date').reset_index(drop=True)
    
    print(f"Time series loaded: {len(daily_ts)} observations")
    print(f"Date range: {daily_ts['Date'].min()} to {daily_ts['Date'].max()}")
    
    return daily_ts

def perform_stationarity_tests(series, name="Series"):
    """Perform comprehensive stationarity tests"""
    print(f"\n{'='*50}")
    print(f"STATIONARITY TESTS FOR {name.upper()}")
    print(f"{'='*50}")
    
    # Augmented Dickey-Fuller Test
    print("1. Augmented Dickey-Fuller Test:")
    adf_result = adfuller(series.dropna(), autolag='AIC')
    print(f"   ADF Statistic: {adf_result[0]:.6f}")
    print(f"   p-value: {adf_result[1]:.6f}")
    print(f"   Critical Values:")
    for key, value in adf_result[4].items():
        print(f"      {key}: {value:.3f}")
    
    adf_stationary = adf_result[1] <= 0.05
    print(f"   Result: {'✅ Stationary' if adf_stationary else '❌ Non-stationary'} (p-value <= 0.05)")
    
    # KPSS Test
    print("\n2. KPSS Test:")
    kpss_result = kpss(series.dropna(), regression='c', nlags='auto')
    print(f"   KPSS Statistic: {kpss_result[0]:.6f}")
    print(f"   p-value: {kpss_result[1]:.6f}")
    print(f"   Critical Values:")
    for key, value in kpss_result[3].items():
        print(f"      {key}: {value:.3f}")
    
    kpss_stationary = kpss_result[1] > 0.05
    print(f"   Result: {'✅ Stationary' if kpss_stationary else '❌ Non-stationary'} (p-value > 0.05)")
    
    # Combined interpretation
    if adf_stationary and kpss_stationary:
        overall_result = "✅ STATIONARY (Both tests agree)"
    elif not adf_stationary and not kpss_stationary:
        overall_result = "❌ NON-STATIONARY (Both tests agree)"
    else:
        overall_result = "⚠️ INCONCLUSIVE (Tests disagree - may need differencing)"
    
    print(f"\n🎯 Overall Assessment: {overall_result}")
    
    return {
        'adf_statistic': adf_result[0],
        'adf_pvalue': adf_result[1],
        'adf_stationary': adf_stationary,
        'kpss_statistic': kpss_result[0],
        'kpss_pvalue': kpss_result[1],
        'kpss_stationary': kpss_stationary,
        'is_stationary': adf_stationary and kpss_stationary,
        'overall_result': overall_result
    }

def check_variance_stability(series, name="Series"):
    """Check variance stability and recommend transformations"""
    print(f"\n{'='*40}")
    print(f"VARIANCE STABILITY ANALYSIS - {name.upper()}")
    print(f"{'='*40}")
    
    # Split series into halves
    mid_point = len(series) // 2
    first_half = series[:mid_point]
    second_half = series[mid_point:]
    
    # Calculate variance statistics
    first_var = first_half.var()
    second_var = second_half.var()
    variance_ratio = second_var / first_var
    
    print(f"First half variance: {first_var:.2f}")
    print(f"Second half variance: {second_var:.2f}")
    print(f"Variance ratio (2nd/1st): {variance_ratio:.3f}")
    
    # Levene's test for equal variances
    levene_stat, levene_p = stats.levene(first_half, second_half)
    print(f"Levene's test p-value: {levene_p:.6f}")
    
    # Recommendations
    if variance_ratio > 2.0 or variance_ratio < 0.5 or levene_p < 0.05:
        print("⚠️ UNSTABLE VARIANCE - Log transformation recommended")
        needs_transformation = True
    else:
        print("✅ STABLE VARIANCE - No transformation needed")
        needs_transformation = False
    
    return {
        'first_half_var': first_var,
        'second_half_var': second_var,
        'variance_ratio': variance_ratio,
        'levene_pvalue': levene_p,
        'needs_transformation': needs_transformation
    }

def apply_log_transformation(series):
    """Apply log transformation to stabilize variance"""
    print("\nApplying log transformation...")
    
    # Ensure all values are positive
    min_val = series.min()
    if min_val <= 0:
        # Add constant to make all values positive
        constant = abs(min_val) + 1
        series_positive = series + constant
        print(f"Added constant {constant} to ensure positive values")
    else:
        series_positive = series
    
    # Apply log transformation
    log_series = np.log(series_positive)
    
    print(f"Original series range: {series.min():.2f} to {series.max():.2f}")
    print(f"Log-transformed range: {log_series.min():.4f} to {log_series.max():.4f}")
    
    return log_series, series_positive

def apply_differencing(series, max_diff=2):
    """Apply differencing to achieve stationarity"""
    print(f"\n{'='*40}")
    print("DIFFERENCING ANALYSIS")
    print(f"{'='*40}")
    
    differenced_series = {}
    stationarity_results = {}
    
    # Test original series
    current_series = series.copy()
    stationarity_results[0] = perform_stationarity_tests(current_series, "Original")
    differenced_series[0] = current_series
    
    # Apply differencing up to max_diff times
    for d in range(1, max_diff + 1):
        print(f"\n{'-'*30}")
        print(f"DIFFERENCING ORDER: {d}")
        print(f"{'-'*30}")
        
        current_series = current_series.diff().dropna()
        differenced_series[d] = current_series
        
        if len(current_series) < 50:  # Minimum length check
            print(f"⚠️ Series too short after {d} differences ({len(current_series)} obs)")
            break
            
        stationarity_results[d] = perform_stationarity_tests(current_series, f"Differenced (d={d})")
        
        # Stop if stationary
        if stationarity_results[d]['is_stationary']:
            print(f"✅ Achieved stationarity with d={d}")
            break
    
    # Find optimal differencing order
    optimal_d = 0
    for d in range(max_diff + 1):
        if d in stationarity_results and stationarity_results[d]['is_stationary']:
            optimal_d = d
            break
    
    print(f"\n🎯 OPTIMAL DIFFERENCING ORDER: d={optimal_d}")
    
    return differenced_series, stationarity_results, optimal_d

def detect_seasonal_patterns(series, seasonal_periods=[7, 30, 365]):
    """Detect seasonal patterns in the time series"""
    print(f"\n{'='*40}")
    print("SEASONAL PATTERN DETECTION")
    print(f"{'='*40}")
    
    seasonal_info = {}
    
    for period in seasonal_periods:
        if len(series) >= 2 * period:  # Need at least 2 full cycles
            # Calculate seasonal decomposition manually
            seasonal_means = []
            for i in range(period):
                seasonal_values = series[i::period]
                if len(seasonal_values) > 1:
                    seasonal_means.append(seasonal_values.mean())
                else:
                    seasonal_means.append(0)
            
            seasonal_strength = np.std(seasonal_means) / np.mean(seasonal_means) if np.mean(seasonal_means) > 0 else 0
            
            seasonal_info[period] = {
                'strength': seasonal_strength,
                'means': seasonal_means
            }
            
            print(f"Period {period}: Seasonal strength = {seasonal_strength:.4f}")
        else:
            print(f"Period {period}: Insufficient data (need {2*period}, have {len(series)})")
    
    # Determine if seasonal modeling is needed
    strong_seasonality = any(info['strength'] > 0.1 for info in seasonal_info.values())
    
    if strong_seasonality:
        print("✅ SEASONAL PATTERNS DETECTED - SARIMA recommended")
        # Find the most significant seasonal period
        best_period = max(seasonal_info.keys(), key=lambda k: seasonal_info[k]['strength'])
        print(f"🎯 Primary seasonal period: {best_period}")
    else:
        print("⚠️ WEAK SEASONAL PATTERNS - Simple ARIMA may suffice")
        best_period = None
    
    return seasonal_info, strong_seasonality, best_period

def create_preprocessed_datasets(daily_ts, optimal_d, log_transformed=False, seasonal_period=None):
    """Create final preprocessed datasets for ARIMA modeling"""
    print(f"\n{'='*50}")
    print("CREATING PREPROCESSED DATASETS")
    print(f"{'='*50}")
    
    # Start with original revenue series
    revenue_series = daily_ts['DailyRevenue'].copy()
    
    # Apply log transformation if needed
    if log_transformed:
        revenue_series, _ = apply_log_transformation(revenue_series)
        transformation_note = "log-transformed"
    else:
        transformation_note = "original scale"
    
    # Apply optimal differencing
    final_series = revenue_series.copy()
    for d in range(optimal_d):
        final_series = final_series.diff().dropna()
    
    # Create final dataset
    final_data = daily_ts.iloc[optimal_d:].copy().reset_index(drop=True)
    final_data['ProcessedRevenue'] = final_series.values
    
    print(f"Final dataset characteristics:")
    print(f"- Transformation: {transformation_note}")
    print(f"- Differencing order: {optimal_d}")
    print(f"- Final series length: {len(final_data)}")
    print(f"- Date range: {final_data['Date'].min()} to {final_data['Date'].max()}")
    
    # Save preprocessed data
    final_data.to_csv('preprocessed_timeseries.csv', index=False)
    
    # Create modeling parameters file
    preprocessing_params = {
        'log_transformed': log_transformed,
        'differencing_order': optimal_d,
        'seasonal_period': seasonal_period,
        'final_series_length': len(final_data),
        'transformation_note': transformation_note
    }
    
    import json
    with open('preprocessing_parameters.json', 'w') as f:
        json.dump(preprocessing_params, f, indent=2)
    
    print(f"\n✅ Preprocessed data saved to: preprocessed_timeseries.csv")
    print(f"✅ Parameters saved to: preprocessing_parameters.json")
    
    return final_data, preprocessing_params

def generate_preprocessing_visualizations(daily_ts, differenced_series, log_transformed_series=None):
    """Generate visualizations of preprocessing steps"""
    print("\nGenerating preprocessing visualizations...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('ARIMA Preprocessing Analysis', fontsize=16, fontweight='bold')
    
    # Original series
    axes[0, 0].plot(daily_ts['Date'], daily_ts['DailyRevenue'])
    axes[0, 0].set_title('Original Daily Revenue')
    axes[0, 0].set_ylabel('Revenue ($)')
    axes[0, 0].tick_params(axis='x', rotation=45)
    
    # Log transformed (if applied)
    if log_transformed_series is not None:
        axes[0, 1].plot(daily_ts['Date'], log_transformed_series)
        axes[0, 1].set_title('Log-Transformed Series')
        axes[0, 1].set_ylabel('Log(Revenue)')
    else:
        axes[0, 1].plot(daily_ts['Date'], daily_ts['DailyRevenue'])
        axes[0, 1].set_title('Original Series (No Log Transform)')
        axes[0, 1].set_ylabel('Revenue ($)')
    axes[0, 1].tick_params(axis='x', rotation=45)
    
    # First difference
    if 1 in differenced_series:
        diff_dates = daily_ts['Date'][1:]  # Skip first date due to differencing
        axes[1, 0].plot(diff_dates, differenced_series[1])
        axes[1, 0].set_title('First Differenced Series')
        axes[1, 0].set_ylabel('Δ Revenue')
        axes[1, 0].tick_params(axis='x', rotation=45)
    
    # ACF of final series
    from statsmodels.tsa.stattools import acf
    if len(differenced_series) > 0:
        final_series = list(differenced_series.values())[-1]
        lags = min(40, len(final_series) // 4)
        acf_values = acf(final_series.dropna(), nlags=lags)
        axes[1, 1].bar(range(len(acf_values)), acf_values)
        axes[1, 1].set_title('ACF of Final Preprocessed Series')
        axes[1, 1].set_xlabel('Lag')
        axes[1, 1].set_ylabel('Autocorrelation')
        axes[1, 1].axhline(y=0, color='black', linestyle='-', alpha=0.3)
        axes[1, 1].axhline(y=0.05, color='red', linestyle='--', alpha=0.5)
        axes[1, 1].axhline(y=-0.05, color='red', linestyle='--', alpha=0.5)
    
    plt.tight_layout()
    plt.savefig('arima_preprocessing_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✅ Preprocessing visualizations saved to: arima_preprocessing_analysis.png")

def main():
    """Main preprocessing pipeline"""
    print("Starting ARIMA data preprocessing pipeline...")
    
    # Load data
    daily_ts = load_time_series_data()
    revenue_series = daily_ts['DailyRevenue']
    
    # Step 1: Check original series stationarity
    original_stationarity = perform_stationarity_tests(revenue_series, "Original Revenue")
    
    # Step 2: Check variance stability
    variance_analysis = check_variance_stability(revenue_series, "Original Revenue")
    
    # Step 3: Apply log transformation if needed
    if variance_analysis['needs_transformation']:
        log_series, _ = apply_log_transformation(revenue_series)
        working_series = log_series
        log_transformed = True
        print("✅ Applied log transformation")
    else:
        working_series = revenue_series
        log_transformed = False
        print("✅ No log transformation needed")
    
    # Step 4: Apply differencing
    differenced_series, stationarity_results, optimal_d = apply_differencing(working_series)
    
    # Step 5: Detect seasonal patterns
    final_series = differenced_series[optimal_d]
    seasonal_info, has_seasonality, seasonal_period = detect_seasonal_patterns(final_series)
    
    # Step 6: Create final preprocessed datasets
    final_data, preprocessing_params = create_preprocessed_datasets(
        daily_ts, optimal_d, log_transformed, seasonal_period
    )
    
    # Step 7: Generate visualizations
    log_series_for_viz = log_series if log_transformed else None
    generate_preprocessing_visualizations(daily_ts, differenced_series, log_series_for_viz)
    
    # Final summary
    print(f"\n{'='*60}")
    print("PREPROCESSING SUMMARY")
    print(f"{'='*60}")
    print(f"✅ Log transformation: {'Applied' if log_transformed else 'Not needed'}")
    print(f"✅ Optimal differencing order: d={optimal_d}")
    print(f"✅ Seasonal patterns: {'Detected' if has_seasonality else 'Weak'}")
    if seasonal_period:
        print(f"✅ Primary seasonal period: {seasonal_period}")
    print(f"✅ Final series length: {len(final_data)} observations")
    print(f"✅ Ready for ARIMA modeling: {'SARIMA' if has_seasonality else 'ARIMA'}")
    
    return final_data, preprocessing_params, has_seasonality, seasonal_period

if __name__ == "__main__":
    processed_data, params, seasonality, s_period = main()
