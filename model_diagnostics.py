#!/usr/bin/env python3
"""
Model Diagnostics and Validation Script
This script performs comprehensive diagnostics on the best ARIMA model.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import json
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.stats.diagnostic import acorr_ljungbox
from statsmodels.tsa.stattools import adfuller
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def load_best_model_info():
    """Load information about the best performing model"""
    print("Loading best model information...")
    
    # Load model evaluation summary
    try:
        with open('model_evaluation_summary.json', 'r') as f:
            evaluation = json.load(f)
        
        best_model_info = evaluation['best_model']
        print(f"✅ Best model: {best_model_info['Model']} ({best_model_info['Type']})")
        
        return best_model_info
    except FileNotFoundError:
        print("❌ Model evaluation summary not found")
        return None

def load_data_and_refit_model(model_info):
    """Load data and refit the best model for diagnostics"""
    print(f"\nRefitting {model_info['Model']} for diagnostics...")
    
    # Load preprocessed data
    data = pd.read_csv('preprocessed_timeseries.csv')
    data['Date'] = pd.to_datetime(data['Date'])
    data = data.sort_values('Date').reset_index(drop=True)
    
    # Split data (same as used in optimization)
    n_test = int(len(data) * 0.2)
    train_data = data.iloc[:-n_test].copy()
    test_data = data.iloc[-n_test:].copy()
    
    train_series = train_data['ProcessedRevenue']
    test_series = test_data['ProcessedRevenue']
    
    # Extract model order from grid search results
    with open('grid_search_results.json', 'r') as f:
        grid_results = json.load(f)
    
    order = tuple(grid_results['best_model_order'])
    print(f"Model order: {order}")
    
    # Refit the model
    model = ARIMA(train_series, order=order)
    fitted_model = model.fit()
    
    print(f"✅ Model refitted successfully")
    print(f"   AIC: {fitted_model.aic:.2f}")
    print(f"   BIC: {fitted_model.bic:.2f}")
    
    return fitted_model, train_series, test_series, train_data, test_data

def perform_residual_analysis(fitted_model, train_series):
    """Perform comprehensive residual analysis"""
    print(f"\n{'='*60}")
    print("RESIDUAL ANALYSIS")
    print(f"{'='*60}")
    
    # Get residuals
    residuals = fitted_model.resid
    
    # Basic residual statistics
    print(f"Residual Statistics:")
    print(f"   Mean: {np.mean(residuals):.6f}")
    print(f"   Std: {np.std(residuals):.6f}")
    print(f"   Min: {np.min(residuals):.6f}")
    print(f"   Max: {np.max(residuals):.6f}")
    print(f"   Skewness: {stats.skew(residuals):.4f}")
    print(f"   Kurtosis: {stats.kurtosis(residuals):.4f}")
    
    # Test for normality
    shapiro_stat, shapiro_p = stats.shapiro(residuals)
    print(f"\nNormality Tests:")
    print(f"   Shapiro-Wilk: statistic={shapiro_stat:.4f}, p-value={shapiro_p:.6f}")
    print(f"   Normal residuals: {'✅ YES' if shapiro_p > 0.05 else '❌ NO'}")
    
    # Test for autocorrelation in residuals
    ljung_box = acorr_ljungbox(residuals, lags=10, return_df=True)
    significant_lags = ljung_box[ljung_box['lb_pvalue'] < 0.05]
    
    print(f"\nAutocorrelation Tests (Ljung-Box):")
    if len(significant_lags) == 0:
        print(f"   ✅ No significant autocorrelation detected")
    else:
        print(f"   ❌ Significant autocorrelation at lags: {list(significant_lags.index)}")
    
    # Create residual diagnostic plots
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # Residuals vs fitted
    fitted_values = fitted_model.fittedvalues
    axes[0, 0].scatter(fitted_values, residuals, alpha=0.6)
    axes[0, 0].axhline(y=0, color='red', linestyle='--')
    axes[0, 0].set_title('Residuals vs Fitted Values')
    axes[0, 0].set_xlabel('Fitted Values')
    axes[0, 0].set_ylabel('Residuals')
    axes[0, 0].grid(True, alpha=0.3)
    
    # Q-Q plot
    stats.probplot(residuals, dist="norm", plot=axes[0, 1])
    axes[0, 1].set_title('Q-Q Plot (Normal Distribution)')
    axes[0, 1].grid(True, alpha=0.3)
    
    # Residuals histogram
    axes[1, 0].hist(residuals, bins=20, density=True, alpha=0.7, color='skyblue')
    axes[1, 0].set_title('Residuals Distribution')
    axes[1, 0].set_xlabel('Residuals')
    axes[1, 0].set_ylabel('Density')
    axes[1, 0].grid(True, alpha=0.3)
    
    # Add normal curve overlay
    x = np.linspace(residuals.min(), residuals.max(), 100)
    axes[1, 0].plot(x, stats.norm.pdf(x, np.mean(residuals), np.std(residuals)), 
                    'r-', linewidth=2, label='Normal')
    axes[1, 0].legend()
    
    # Residuals over time
    axes[1, 1].plot(residuals, alpha=0.7)
    axes[1, 1].axhline(y=0, color='red', linestyle='--')
    axes[1, 1].set_title('Residuals Over Time')
    axes[1, 1].set_xlabel('Time')
    axes[1, 1].set_ylabel('Residuals')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('residual_diagnostics.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ Residual diagnostic plots saved to: residual_diagnostics.png")
    
    return {
        'mean': np.mean(residuals),
        'std': np.std(residuals),
        'skewness': stats.skew(residuals),
        'kurtosis': stats.kurtosis(residuals),
        'shapiro_stat': shapiro_stat,
        'shapiro_p': shapiro_p,
        'normal_residuals': shapiro_p > 0.05,
        'autocorrelation_lags': list(significant_lags.index) if len(significant_lags) > 0 else [],
        'no_autocorrelation': len(significant_lags) == 0
    }

def analyze_autocorrelation_structure(residuals):
    """Analyze autocorrelation and partial autocorrelation of residuals"""
    print(f"\n{'='*60}")
    print("AUTOCORRELATION ANALYSIS")
    print(f"{'='*60}")
    
    # Create ACF and PACF plots for residuals
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))
    
    # ACF plot
    plot_acf(residuals, lags=20, ax=axes[0], title='ACF of Residuals')
    axes[0].grid(True, alpha=0.3)
    
    # PACF plot
    plot_pacf(residuals, lags=20, ax=axes[1], title='PACF of Residuals')
    axes[1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('residual_acf_pacf.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ ACF/PACF plots saved to: residual_acf_pacf.png")
    
    # Check for significant autocorrelations
    from statsmodels.tsa.stattools import acf, pacf
    
    acf_values = acf(residuals, nlags=20, alpha=0.05)
    pacf_values = pacf(residuals, nlags=20, alpha=0.05)
    
    # Count significant lags (outside confidence intervals)
    acf_significant = 0
    pacf_significant = 0
    
    if len(acf_values) > 1 and hasattr(acf_values[1], '__len__'):
        # acf returns (values, confidence_intervals)
        acf_vals, acf_confint = acf_values
        for i in range(1, len(acf_vals)):  # Skip lag 0
            if acf_vals[i] < acf_confint[i][0] or acf_vals[i] > acf_confint[i][1]:
                acf_significant += 1
    
    if len(pacf_values) > 1 and hasattr(pacf_values[1], '__len__'):
        # pacf returns (values, confidence_intervals)
        pacf_vals, pacf_confint = pacf_values
        for i in range(1, len(pacf_vals)):  # Skip lag 0
            if pacf_vals[i] < pacf_confint[i][0] or pacf_vals[i] > pacf_confint[i][1]:
                pacf_significant += 1
    
    print(f"Significant autocorrelations: {acf_significant}/20 lags")
    print(f"Significant partial autocorrelations: {pacf_significant}/20 lags")
    
    if acf_significant <= 1 and pacf_significant <= 1:
        print("✅ Residuals show good white noise properties")
    else:
        print("⚠️ Some residual autocorrelation detected - model may need refinement")
    
    return {
        'acf_significant_lags': acf_significant,
        'pacf_significant_lags': pacf_significant,
        'good_white_noise': acf_significant <= 1 and pacf_significant <= 1
    }

def validate_model_assumptions(fitted_model, residual_analysis, autocorr_analysis):
    """Validate key ARIMA model assumptions"""
    print(f"\n{'='*60}")
    print("MODEL ASSUMPTIONS VALIDATION")
    print(f"{'='*60}")
    
    assumptions = {
        'stationarity': True,  # Already validated in preprocessing
        'linearity': True,     # ARIMA assumes linear relationships
        'normal_residuals': residual_analysis['normal_residuals'],
        'no_autocorrelation': residual_analysis['no_autocorrelation'],
        'homoscedasticity': abs(residual_analysis['skewness']) < 1.0,  # Rough check
        'white_noise_residuals': autocorr_analysis['good_white_noise']
    }
    
    print("Assumption Validation:")
    print("-" * 40)
    for assumption, passed in assumptions.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{assumption.replace('_', ' ').title():<25} {status}")
    
    # Overall model validity
    passed_count = sum(assumptions.values())
    total_count = len(assumptions)
    
    print(f"\nOverall Model Validity: {passed_count}/{total_count} assumptions met")
    
    if passed_count >= 4:
        print("✅ Model is generally valid for forecasting")
    else:
        print("⚠️ Model has significant assumption violations")
        print("   Consider model refinement or alternative approaches")
    
    return assumptions

def generate_confidence_intervals_analysis(fitted_model, test_series):
    """Analyze confidence interval coverage and reliability"""
    print(f"\n{'='*60}")
    print("CONFIDENCE INTERVALS ANALYSIS")
    print(f"{'='*60}")
    
    # Generate forecasts with confidence intervals
    n_forecast = len(test_series)
    forecast_result = fitted_model.get_forecast(steps=n_forecast)
    forecasts = forecast_result.predicted_mean
    conf_int = forecast_result.conf_int()
    
    # Calculate coverage
    lower_bound = conf_int.iloc[:, 0]
    upper_bound = conf_int.iloc[:, 1]
    
    within_interval = ((test_series >= lower_bound) & (test_series <= upper_bound)).sum()
    coverage_rate = within_interval / len(test_series) * 100
    
    print(f"95% Confidence Interval Coverage:")
    print(f"   Actual coverage: {coverage_rate:.1f}%")
    print(f"   Expected coverage: 95.0%")
    print(f"   Coverage quality: {'✅ GOOD' if 90 <= coverage_rate <= 98 else '⚠️ POOR'}")
    
    # Analyze interval widths
    interval_widths = upper_bound - lower_bound
    avg_width = np.mean(interval_widths)
    width_cv = np.std(interval_widths) / avg_width
    
    print(f"\nConfidence Interval Width Analysis:")
    print(f"   Average width: {avg_width:.4f}")
    print(f"   Width coefficient of variation: {width_cv:.4f}")
    print(f"   Width consistency: {'✅ GOOD' if width_cv < 0.5 else '⚠️ VARIABLE'}")
    
    return {
        'coverage_rate': coverage_rate,
        'good_coverage': 90 <= coverage_rate <= 98,
        'avg_width': avg_width,
        'width_cv': width_cv,
        'consistent_width': width_cv < 0.5
    }

def create_diagnostic_summary_report(model_info, residual_analysis, autocorr_analysis, 
                                   assumptions, confidence_analysis):
    """Create comprehensive diagnostic summary report"""
    print(f"\n{'='*80}")
    print("COMPREHENSIVE DIAGNOSTIC SUMMARY")
    print(f"{'='*80}")
    
    print(f"Model: {model_info['Model']} ({model_info['Type']})")
    print(f"Performance: MAPE = {model_info['MAPE']:.2f}%, MAE = {model_info['MAE']:.4f}")
    
    # Diagnostic scores
    diagnostic_scores = {
        'Residual Normality': residual_analysis['normal_residuals'],
        'No Autocorrelation': residual_analysis['no_autocorrelation'],
        'White Noise Residuals': autocorr_analysis['good_white_noise'],
        'Good CI Coverage': confidence_analysis['good_coverage'],
        'Consistent CI Width': confidence_analysis['consistent_width']
    }
    
    print(f"\nDiagnostic Test Results:")
    print("-" * 50)
    for test, passed in diagnostic_scores.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test:<25} {status}")
    
    # Overall diagnostic score
    diagnostic_score = sum(diagnostic_scores.values()) / len(diagnostic_scores) * 100
    
    print(f"\nOverall Diagnostic Score: {diagnostic_score:.1f}%")
    
    if diagnostic_score >= 80:
        print("✅ EXCELLENT - Model passes most diagnostic tests")
        recommendation = "Model is ready for production deployment"
    elif diagnostic_score >= 60:
        print("⚠️ GOOD - Model has minor issues but is usable")
        recommendation = "Model can be used with monitoring"
    else:
        print("❌ POOR - Model has significant diagnostic issues")
        recommendation = "Model needs refinement before deployment"
    
    print(f"Recommendation: {recommendation}")
    
    return {
        'diagnostic_score': diagnostic_score,
        'recommendation': recommendation,
        'diagnostic_tests': diagnostic_scores
    }

def main():
    """Main model diagnostics pipeline"""
    print("Starting comprehensive model diagnostics...")
    
    # Load best model information
    model_info = load_best_model_info()
    if model_info is None:
        return None
    
    # Refit model and load data
    fitted_model, train_series, test_series, train_data, test_data = load_data_and_refit_model(model_info)
    
    # Perform residual analysis
    residual_analysis = perform_residual_analysis(fitted_model, train_series)
    
    # Analyze autocorrelation structure
    autocorr_analysis = analyze_autocorrelation_structure(fitted_model.resid)
    
    # Validate model assumptions
    assumptions = validate_model_assumptions(fitted_model, residual_analysis, autocorr_analysis)
    
    # Analyze confidence intervals
    confidence_analysis = generate_confidence_intervals_analysis(fitted_model, test_series)
    
    # Create summary report
    summary = create_diagnostic_summary_report(model_info, residual_analysis, 
                                             autocorr_analysis, assumptions, confidence_analysis)
    
    # Save diagnostic results
    diagnostic_results = {
        'model_info': model_info,
        'residual_analysis': residual_analysis,
        'autocorrelation_analysis': autocorr_analysis,
        'assumptions_validation': assumptions,
        'confidence_intervals_analysis': confidence_analysis,
        'diagnostic_summary': summary,
        'model_parameters': {
            'aic': fitted_model.aic,
            'bic': fitted_model.bic,
            'log_likelihood': fitted_model.llf
        }
    }
    
    with open('model_diagnostics_results.json', 'w') as f:
        json.dump(diagnostic_results, f, indent=2, default=str)
    
    print(f"\n✅ Model diagnostics completed!")
    print(f"✅ Results saved to: model_diagnostics_results.json")
    print(f"✅ Diagnostic plots saved:")
    print(f"   - residual_diagnostics.png")
    print(f"   - residual_acf_pacf.png")
    
    return diagnostic_results

if __name__ == "__main__":
    results = main()
